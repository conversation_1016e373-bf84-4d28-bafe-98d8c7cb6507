{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "47391869ec9213432a83beb6bdd776a7", "packages": [{"name": "aliyuncs/oss-sdk-php", "version": "v2.6.0", "source": {"type": "git", "url": "https://github.com/aliyun/aliyun-oss-php-sdk.git", "reference": "572d0f8e099e8630ae7139ed3fdedb926c7a760f"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/aliyuncs/oss-sdk-php/v2.6.0/aliyuncs-oss-sdk-php-v2.6.0.zip", "reference": "572d0f8e099e8630ae7139ed3fdedb926c7a760f", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "*", "satooshi/php-coveralls": "*"}, "type": "library", "autoload": {"psr-4": {"OSS\\": "src/OSS"}}, "license": ["MIT"], "authors": [{"name": "Aliyuncs", "homepage": "http://www.aliyun.com"}], "description": "Aliyun OSS SDK for PHP", "homepage": "http://www.aliyun.com/product/oss/", "time": "2022-08-03T08:06:01+00:00"}, {"name": "almasaeed2010/adminlte", "version": "v2.4.18", "source": {"type": "git", "url": "https://github.com/ColorlibHQ/AdminLTE.git", "reference": "e7ffa67a4649dc08d2018708a38604a6c0a02ab6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ColorlibHQ/AdminLTE/zipball/e7ffa67a4649dc08d2018708a38604a6c0a02ab6", "reference": "e7ffa67a4649dc08d2018708a38604a6c0a02ab6", "shasum": ""}, "require": {"bower-asset/jquery": ">=1.9.0 <4.0.0"}, "type": "template", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "AdminLTE - admin control panel and dashboard that's based on Bootstrap 3", "homepage": "https://adminlte.io/", "keywords": ["JS", "admin", "back-end", "css", "less", "responsive", "template", "theme", "web"], "support": {"issues": "https://github.com/almasaeed2010/AdminLTE/issues", "source": "https://github.com/ColorlibHQ/AdminLTE/tree/v2.4.18"}, "time": "2019-08-29T08:20:20+00:00"}, {"name": "bower-asset/bootstrap", "version": "v3.4.1", "source": {"type": "git", "url": "https://github.com/twbs/bootstrap.git", "reference": "68b0d231a13201eb14acd3dc84e51543d16e5f7e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twbs/bootstrap/zipball/68b0d231a13201eb14acd3dc84e51543d16e5f7e", "reference": "68b0d231a13201eb14acd3dc84e51543d16e5f7e"}, "require": {"bower-asset/jquery": ">=1.9.1,<4.0"}, "type": "bower-asset", "license": ["MIT"]}, {"name": "bower-asset/inputmask", "version": "5.0.9", "source": {"type": "git", "url": "https://github.com/RobinHerbots/Inputmask.git", "reference": "310a33557e2944daf86d5946a5e8c82b9118f8f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/RobinHerbots/Inputmask/zipball/310a33557e2944daf86d5946a5e8c82b9118f8f7", "reference": "310a33557e2944daf86d5946a5e8c82b9118f8f7"}, "require": {"bower-asset/jquery": ">=1.7"}, "type": "bower-asset", "license": ["http://opensource.org/licenses/mit-license.php"]}, {"name": "bower-asset/jquery", "version": "3.7.1", "source": {"type": "git", "url": "https://github.com/jquery/jquery-dist.git", "reference": "fde1f76e2799dd877c176abde0ec836553246991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jquery/jquery-dist/zipball/fde1f76e2799dd877c176abde0ec836553246991", "reference": "fde1f76e2799dd877c176abde0ec836553246991"}, "type": "bower-asset", "license": ["MIT"]}, {"name": "bower-asset/punycode", "version": "v1.4.1", "source": {"type": "git", "url": "https://github.com/mathiasbynens/punycode.js.git", "reference": "0fbadd6e81f3a0ce06c38998040d6db6bdfbc5c9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mathiasbynens/punycode.js/zipball/0fbadd6e81f3a0ce06c38998040d6db6bdfbc5c9", "reference": "0fbadd6e81f3a0ce06c38998040d6db6bdfbc5c9"}, "type": "bower-asset"}, {"name": "bower-asset/yii2-pjax", "version": "2.0.8", "source": {"type": "git", "url": "https://github.com/yiisoft/jquery-pjax.git", "reference": "a9298d57da63d14a950f1b94366a864bc62264fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/jquery-pjax/zipball/a9298d57da63d14a950f1b94366a864bc62264fb", "reference": "a9298d57da63d14a950f1b94366a864bc62264fb"}, "require": {"bower-asset/jquery": ">=1.8"}, "type": "bower-asset", "license": ["MIT"]}, {"name": "carbonphp/carbon-doctrine-types", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/CarbonPHP/carbon-doctrine-types.git", "reference": "99f76ffa36cce3b70a4a6abce41dba15ca2e84cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/CarbonPHP/carbon-doctrine-types/zipball/99f76ffa36cce3b70a4a6abce41dba15ca2e84cb", "reference": "99f76ffa36cce3b70a4a6abce41dba15ca2e84cb", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "conflict": {"doctrine/dbal": "<3.7.0 || >=4.0.0"}, "require-dev": {"doctrine/dbal": "^3.7.0", "nesbot/carbon": "^2.71.0 || ^3.0.0", "phpunit/phpunit": "^10.3"}, "type": "library", "autoload": {"psr-4": {"Carbon\\Doctrine\\": "src/Carbon/Doctrine/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KyleKatarn", "email": "<EMAIL>"}], "description": "Types to use Carbon in Doctrine", "keywords": ["carbon", "date", "datetime", "doctrine", "time"], "support": {"issues": "https://github.com/CarbonPHP/carbon-doctrine-types/issues", "source": "https://github.com/CarbonPHP/carbon-doctrine-types/tree/2.1.0"}, "funding": [{"url": "https://github.com/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/nesbot/carbon", "type": "tidelift"}], "time": "2023-12-11T17:09:12+00:00"}, {"name": "cebe/markdown", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/cebe/markdown.git", "reference": "9bac5e971dd391e2802dca5400bbeacbaea9eb86"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cebe/markdown/zipball/9bac5e971dd391e2802dca5400bbeacbaea9eb86", "reference": "9bac5e971dd391e2802dca5400bbeacbaea9eb86", "shasum": ""}, "require": {"lib-pcre": "*", "php": ">=5.4.0"}, "require-dev": {"cebe/indent": "*", "facebook/xhprof": "*@dev", "phpunit/phpunit": "4.1.*"}, "bin": ["bin/markdown"], "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"cebe\\markdown\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://cebe.cc/", "role": "Creator"}], "description": "A super fast, highly extensible markdown parser for PHP", "homepage": "https://github.com/cebe/markdown#readme", "keywords": ["extensible", "fast", "gfm", "markdown", "markdown-extra"], "support": {"issues": "https://github.com/cebe/markdown/issues", "source": "https://github.com/cebe/markdown"}, "time": "2018-03-26T11:24:36+00:00"}, {"name": "clue/stream-filter", "version": "v1.7.0", "source": {"type": "git", "url": "https://github.com/clue/stream-filter.git", "reference": "049509fef80032cb3f051595029ab75b49a3c2f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/clue/stream-filter/zipball/049509fef80032cb3f051595029ab75b49a3c2f7", "reference": "049509fef80032cb3f051595029ab75b49a3c2f7", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"Clue\\StreamFilter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A simple and modern approach to stream filtering in PHP", "homepage": "https://github.com/clue/stream-filter", "keywords": ["bucket brigade", "callback", "filter", "php_user_filter", "stream", "stream_filter_append", "stream_filter_register"], "support": {"issues": "https://github.com/clue/stream-filter/issues", "source": "https://github.com/clue/stream-filter/tree/v1.7.0"}, "funding": [{"url": "https://clue.engineering/support", "type": "custom"}, {"url": "https://github.com/clue", "type": "github"}], "time": "2023-12-20T15:40:13+00:00"}, {"name": "codingheping/code-diff", "version": "1.2.0", "source": {"type": "git", "url": "************************:xlerr/code-diff.git", "reference": "7073ddc7ce3021677d7d31f1547d43398ba0e147"}, "dist": {"type": "zip", "url": "https://biz-satis-test.kuainiujinke.com/composer/dist/codingheping/code-diff/codingheping-code-diff-1.2.0-1b73b4.zip", "reference": "7073ddc7ce3021677d7d31f1547d43398ba0e147"}, "require": {"ext-json": "*", "php": ">=7.4", "xlerr/yii2-common-widgets": "^1.5"}, "require-dev": {"yiisoft/yii2-debug": "~2.0.0"}, "type": "yii2-extension", "autoload": {"psr-4": {"Diff\\": "src/"}}, "autoload-dev": {"psr-4": {"Diff\\Test\\": "test/"}}, "license": ["MIT"], "authors": [{"name": "heping", "email": "<EMAIL>"}], "description": "description", "time": "2023-12-25T07:49:34+00:00"}, {"name": "codingheping/import", "version": "v1.8.2", "source": {"type": "git", "url": "************************:xlerr/import.git", "reference": "4f59fcb8f993fef823ffc30fef20d93eb9fa3387"}, "dist": {"type": "zip", "url": "https://biz-satis-test.kuainiujinke.com/composer/dist/codingheping/import/codingheping-import-v1.8.2-d9e44a.zip", "reference": "4f59fcb8f993fef823ffc30fef20d93eb9fa3387"}, "require": {"ext-json": "*", "nesbot/carbon": "^2.35", "phpoffice/phpspreadsheet": "^1.12", "xlerr/yii2-common-widgets": "^1.8", "yiisoft/yii2": "^2.0"}, "type": "yii2-extension", "autoload": {"psr-4": {"import\\": "src/"}}, "autoload-dev": {"psr-4": {"import\\examples\\": "examples/", "import\\test\\": "test/"}}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "KuaiNiu Group Import File Component", "keywords": ["import", "kuainiu"], "time": "2024-04-17T06:15:39+00:00"}, {"name": "codingheping/statement-component", "version": "v1.2.0", "source": {"type": "git", "url": "************************:xlerr/statement.git", "reference": "ea315b831048bc9ba6a6919d256c2f8b134a0ff3"}, "dist": {"type": "zip", "url": "https://biz-satis-test.kuainiujinke.com/composer/dist/codingheping/statement-component/codingheping-statement-component-v1.2.0-587191.zip", "reference": "ea315b831048bc9ba6a6919d256c2f8b134a0ff3"}, "require": {"codingheping/code-diff": "^1.0", "ext-json": "*", "ext-mbstring": "*", "kartik-v/yii2-tabs-x": "^1.2", "php": ">=7.4", "xlerr/kvmanager": ">= 4.13 < 5.0", "xlerr/yii2-adminlte": "^2.0 || ^3.0", "xlerr/yii2-common-widgets": "^1.8", "yiisoft/yii2": "^2.0"}, "require-dev": {"roave/security-advisories": "dev-latest", "yiisoft/yii2-debug": "^2.0"}, "type": "yii2-extension", "autoload": {"psr-4": {"Codingheping\\StatementComponent\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "he ping", "email": "<EMAIL>"}], "description": "清结算-对账组件", "time": "2024-01-02T03:31:06+00:00"}, {"name": "codingheping/web-task", "version": "1.1.3", "source": {"type": "git", "url": "************************:xlerr/web-task.git", "reference": "8214826e82495c93ca95cda7f2636b29accc08b5"}, "dist": {"type": "zip", "url": "https://biz-satis-test.kuainiujinke.com/composer/dist/codingheping/web-task/codingheping-web-task-1.1.3-4ac268.zip", "reference": "8214826e82495c93ca95cda7f2636b29accc08b5"}, "require": {"php": ">=7.4", "xlerr/yii2-common-widgets": ">=1.13.0 < 2.0", "xlerr/yii2-widget-code-editor": "^1.1", "yiisoft/yii2": ">=2.0.13 <3.0"}, "type": "yii2-extension", "autoload": {"psr-4": {"Codingheping\\WebTask\\": "src"}}, "license": ["MIT"], "authors": [{"name": "heping", "email": "<EMAIL>"}], "description": "biz web task", "time": "2024-04-23T12:04:16+00:00"}, {"name": "components/font-awesome", "version": "5.15.4", "source": {"type": "git", "url": "https://github.com/components/font-awesome.git", "reference": "e6fd09f30f578915cc0cf186b0dd0da54385b6be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/components/font-awesome/zipball/e6fd09f30f578915cc0cf186b0dd0da54385b6be", "reference": "e6fd09f30f578915cc0cf186b0dd0da54385b6be", "shasum": ""}, "type": "component", "extra": {"component": {"files": ["css/all.min.css", "webfonts/*"], "styles": ["css/all.css"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["CC-BY-4.0", "MIT", "OFL-1.1"], "description": "Font Awesome, the iconic SVG, font, and CSS framework.", "support": {"issues": "https://github.com/components/font-awesome/issues", "source": "https://github.com/components/font-awesome/tree/5.15.4"}, "time": "2021-08-15T10:31:24+00:00"}, {"name": "composer/pcre", "version": "3.3.2", "source": {"type": "git", "url": "https://github.com/composer/pcre.git", "reference": "b2bed4734f0cc156ee1fe9c0da2550420d99a21e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/pcre/zipball/b2bed4734f0cc156ee1fe9c0da2550420d99a21e", "reference": "b2bed4734f0cc156ee1fe9c0da2550420d99a21e", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "conflict": {"phpstan/phpstan": "<1.11.10"}, "require-dev": {"phpstan/phpstan": "^1.12 || ^2", "phpstan/phpstan-strict-rules": "^1 || ^2", "phpunit/phpunit": "^8 || ^9"}, "type": "library", "extra": {"phpstan": {"includes": ["extension.neon"]}, "branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Pcre\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "PCRE wrapping library that offers type-safe preg_* replacements.", "keywords": ["PCRE", "preg", "regex", "regular expression"], "support": {"issues": "https://github.com/composer/pcre/issues", "source": "https://github.com/composer/pcre/tree/3.3.2"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-11-12T16:29:46+00:00"}, {"name": "doctrine/deprecations", "version": "1.1.5", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"phpunit/phpunit": "<=7.5 || >=13"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12 || ^13", "phpstan/phpstan": "1.4.10 || 2.1.11", "phpstan/phpstan-phpunit": "^1.0 || ^2", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6 || ^10.5 || ^11.5 || ^12", "psr/log": "^1 || ^2 || ^3"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.5"}, "time": "2025-04-07T20:06:18+00:00"}, {"name": "doctrine/lexer", "version": "2.1.1", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6", "reference": "861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^4.11 || ^5.21"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/2.1.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2024-02-05T11:35:39+00:00"}, {"name": "doctrine/sql-formatter", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/doctrine/sql-formatter.git", "reference": "3447381095d32a171fe3a58323749f44dbb5ac7d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/sql-formatter/zipball/3447381095d32a171fe3a58323749f44dbb5ac7d", "reference": "3447381095d32a171fe3a58323749f44dbb5ac7d", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0", "phpstan/phpstan": "^1.0", "phpunit/phpunit": "^8.5 || ^9.6", "vimeo/psalm": "^4.11"}, "bin": ["bin/sql-formatter"], "type": "library", "autoload": {"psr-4": {"Doctrine\\SqlFormatter\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://jeremydorn.com/"}], "description": "a PHP SQL highlighting library", "homepage": "https://github.com/doctrine/sql-formatter/", "keywords": ["highlight", "sql"], "support": {"issues": "https://github.com/doctrine/sql-formatter/issues", "source": "https://github.com/doctrine/sql-formatter/tree/1.3.0"}, "time": "2024-05-06T21:49:18+00:00"}, {"name": "dragonmantank/cron-expression", "version": "v3.4.0", "source": {"type": "git", "url": "https://github.com/dragonmantank/cron-expression.git", "reference": "8c784d071debd117328803d86b2097615b457500"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/8c784d071debd117328803d86b2097615b457500", "reference": "8c784d071debd117328803d86b2097615b457500", "shasum": ""}, "require": {"php": "^7.2|^8.0", "webmozart/assert": "^1.0"}, "replace": {"mtdowling/cron-expression": "^1.0"}, "require-dev": {"phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.0", "phpunit/phpunit": "^7.0|^8.0|^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Cron\\": "src/Cron/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/dragonmantank"}], "description": "CRON for PHP: Calculate the next or previous run date and determine if a CRON expression is due", "keywords": ["cron", "schedule"], "support": {"issues": "https://github.com/dragonmantank/cron-expression/issues", "source": "https://github.com/dragonmantank/cron-expression/tree/v3.4.0"}, "funding": [{"url": "https://github.com/dragonmantank", "type": "github"}], "time": "2024-10-09T13:47:03+00:00"}, {"name": "egulias/email-validator", "version": "3.2.6", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "e5997fa97e8790cdae03a9cbd5e78e45e3c7bda7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/e5997fa97e8790cdae03a9cbd5e78e45e3c7bda7", "reference": "e5997fa97e8790cdae03a9cbd5e78e45e3c7bda7", "shasum": ""}, "require": {"doctrine/lexer": "^1.2|^2", "php": ">=7.2", "symfony/polyfill-intl-idn": "^1.15"}, "require-dev": {"phpunit/phpunit": "^8.5.8|^9.3.3", "vimeo/psalm": "^4"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/3.2.6"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2023-06-01T07:04:22+00:00"}, {"name": "ezyang/htmlpurifier", "version": "v4.18.0", "source": {"type": "git", "url": "https://github.com/ezyang/htmlpurifier.git", "reference": "cb56001e54359df7ae76dc522d08845dc741621b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/cb56001e54359df7ae76dc522d08845dc741621b", "reference": "cb56001e54359df7ae76dc522d08845dc741621b", "shasum": ""}, "require": {"php": "~5.6.0 || ~7.0.0 || ~7.1.0 || ~7.2.0 || ~7.3.0 || ~7.4.0 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "require-dev": {"cerdic/css-tidy": "^1.7 || ^2.0", "simpletest/simpletest": "dev-master"}, "suggest": {"cerdic/css-tidy": "If you want to use the filter 'Filter.ExtractStyleBlocks'.", "ext-bcmath": "Used for unit conversion and imagecrash protection", "ext-iconv": "Converts text to and from non-UTF-8 encodings", "ext-tidy": "Used for pretty-printing HTML"}, "type": "library", "autoload": {"files": ["library/HTMLPurifier.composer.php"], "psr-0": {"HTMLPurifier": "library/"}, "exclude-from-classmap": ["/library/HTMLPurifier/Language/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "support": {"issues": "https://github.com/ezyang/htmlpurifier/issues", "source": "https://github.com/ezyang/htmlpurifier/tree/v4.18.0"}, "time": "2024-11-01T03:51:45+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.9.3", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^2.7.0", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.9.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2025-03-27T13:37:11+00:00"}, {"name": "guzzlehttp/promises", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/7c69f28996b0a6920945dd20b3857e499d9ca96c", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.2.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2025-03-27T13:27:01+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.7.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/c2270caaabe631b3b44c85f99e5a04bbb8060d16", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "0.9.0", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.7.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2025-03-27T12:30:47+00:00"}, {"name": "http-interop/http-factory-guzzle", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/http-interop/http-factory-guzzle.git", "reference": "8f06e92b95405216b237521cc64c804dd44c4a81"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/http-interop/http-factory-guzzle/zipball/8f06e92b95405216b237521cc64c804dd44c4a81", "reference": "8f06e92b95405216b237521cc64c804dd44c4a81", "shasum": ""}, "require": {"guzzlehttp/psr7": "^1.7||^2.0", "php": ">=7.3", "psr/http-factory": "^1.0"}, "provide": {"psr/http-factory-implementation": "^1.0"}, "require-dev": {"http-interop/http-factory-tests": "^0.9", "phpunit/phpunit": "^9.5"}, "suggest": {"guzzlehttp/psr7": "Includes an HTTP factory starting in version 2.0"}, "type": "library", "autoload": {"psr-4": {"Http\\Factory\\Guzzle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "An HTTP Factory using Guzzle PSR7", "keywords": ["factory", "http", "psr-17", "psr-7"], "support": {"issues": "https://github.com/http-interop/http-factory-guzzle/issues", "source": "https://github.com/http-interop/http-factory-guzzle/tree/1.2.0"}, "time": "2021-07-21T13:50:14+00:00"}, {"name": "jean85/pretty-package-versions", "version": "2.1.1", "source": {"type": "git", "url": "https://github.com/Jean85/pretty-package-versions.git", "reference": "4d7aa5dab42e2a76d99559706022885de0e18e1a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Jean85/pretty-package-versions/zipball/4d7aa5dab42e2a76d99559706022885de0e18e1a", "reference": "4d7aa5dab42e2a76d99559706022885de0e18e1a", "shasum": ""}, "require": {"composer-runtime-api": "^2.1.0", "php": "^7.4|^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "jean85/composer-provided-replaced-stub-package": "^1.0", "phpstan/phpstan": "^2.0", "phpunit/phpunit": "^7.5|^8.5|^9.6", "rector/rector": "^2.0", "vimeo/psalm": "^4.3 || ^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Jean85\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to get pretty versions strings of installed dependencies", "keywords": ["composer", "package", "release", "versions"], "support": {"issues": "https://github.com/Jean85/pretty-package-versions/issues", "source": "https://github.com/Jean85/pretty-package-versions/tree/2.1.1"}, "time": "2025-03-19T14:43:43+00:00"}, {"name": "kartik-v/bootstrap-fileinput", "version": "v5.5.4", "source": {"type": "git", "url": "https://github.com/kartik-v/bootstrap-fileinput.git", "reference": "8de1bed638823c70272b2578847e2b31e42677ba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/bootstrap-fileinput/zipball/8de1bed638823c70272b2578847e2b31e42677ba", "reference": "8de1bed638823c70272b2578847e2b31e42677ba", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.5.x-dev"}}, "autoload": {"psr-4": {"kartik\\plugins\\fileinput\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An enhanced HTML 5 file input for Bootstrap 5.x, 4.x, and 3.x with features for file preview for many file types, multiple selection, ajax uploads, and more.", "homepage": "https://github.com/kartik-v/bootstrap-fileinput", "keywords": ["ajax", "bootstrap", "delete", "file", "image", "input", "j<PERSON>y", "multiple", "preview", "progress", "upload"], "support": {"issues": "https://github.com/kartik-v/bootstrap-fileinput/issues", "source": "https://github.com/kartik-v/bootstrap-fileinput/tree/v5.5.4"}, "funding": [{"url": "https://opencollective.com/bootstrap-fileinput", "type": "open_collective"}], "time": "2024-04-09T03:30:45+00:00"}, {"name": "kartik-v/bootstrap-popover-x", "version": "v1.5.4", "source": {"type": "git", "url": "https://github.com/kartik-v/bootstrap-popover-x.git", "reference": "879def62529797833e10885b6e77864e83e9e240"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/bootstrap-popover-x/zipball/879def62529797833e10885b6e77864e83e9e240", "reference": "879def62529797833e10885b6e77864e83e9e240", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}}, "autoload": {"psr-4": {"kartik\\plugins\\popover\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Bootstrap Popover Extended - Popover with modal behavior, styling enhancements and more.", "homepage": "https://github.com/kartik-v/bootstrap-popover-x", "keywords": ["bootstrap", "extended", "j<PERSON>y", "modal", "modal-popover", "popover", "popover-x"], "support": {"issues": "https://github.com/kartik-v/bootstrap-popover-x/issues", "source": "https://github.com/kartik-v/bootstrap-popover-x/tree/v1.5.4"}, "funding": [{"url": "https://opencollective.com/bootstrap-popover-x", "type": "open_collective"}], "time": "2024-03-12T13:23:15+00:00"}, {"name": "kartik-v/bootstrap-star-rating", "version": "v4.1.2", "source": {"type": "git", "url": "https://github.com/kartik-v/bootstrap-star-rating.git", "reference": "c301efed4c82e9d5f11a0845ae428ba60931b44e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/bootstrap-star-rating/zipball/c301efed4c82e9d5f11a0845ae428ba60931b44e", "reference": "c301efed4c82e9d5f11a0845ae428ba60931b44e", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.1.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A simple yet powerful JQuery star rating plugin for Bootstrap.", "homepage": "https://github.com/kartik-v/bootstrap-star-rating", "keywords": ["Rating", "awesome", "bootstrap", "font", "glyphicon", "star", "svg"], "support": {"issues": "https://github.com/kartik-v/bootstrap-star-rating/issues", "source": "https://github.com/kartik-v/bootstrap-star-rating/tree/v4.1.2"}, "funding": [{"url": "https://opencollective.com/bootstrap-star-rating", "type": "open_collective"}], "time": "2021-09-20T03:06:01+00:00"}, {"name": "kartik-v/bootstrap-tabs-x", "version": "v1.3.5", "source": {"type": "git", "url": "https://github.com/kartik-v/bootstrap-tabs-x.git", "reference": "12e5eeceda5ecbdf1bc7e429c4cea69c0e0b5267"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/bootstrap-tabs-x/zipball/12e5eeceda5ecbdf1bc7e429c4cea69c0e0b5267", "reference": "12e5eeceda5ecbdf1bc7e429c4cea69c0e0b5267", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"kartik\\plugins\\tabs\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Extended Bootstrap Tabs with ability to align tabs in multiple ways, add borders, rotated titles, and more.", "homepage": "https://github.com/kartik-v/bootstrap-tabs-x", "keywords": ["bootstrap", "extended", "j<PERSON>y", "modal", "modal-tabs", "tabs", "tabs-x"], "support": {"issues": "https://github.com/kartik-v/bootstrap-tabs-x/issues", "source": "https://github.com/kartik-v/bootstrap-tabs-x/tree/v1.3.5"}, "time": "2021-09-21T02:01:51+00:00"}, {"name": "kartik-v/dependent-dropdown", "version": "v1.4.9", "source": {"type": "git", "url": "https://github.com/kartik-v/dependent-dropdown.git", "reference": "54a8806002ee21b744508a2edb95ed01d35c6cf9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/dependent-dropdown/zipball/54a8806002ee21b744508a2edb95ed01d35c6cf9", "reference": "54a8806002ee21b744508a2edb95ed01d35c6cf9", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-4": {"kartik\\plugins\\depdrop\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A multi level dependent dropdown JQuery plugin that allows nested dependencies.", "homepage": "https://github.com/kartik-v/dependent-dropdown", "keywords": ["dependent", "dropdown", "j<PERSON>y", "option", "select"], "support": {"issues": "https://github.com/kartik-v/dependent-dropdown/issues", "source": "https://github.com/kartik-v/dependent-dropdown/tree/master"}, "time": "2019-03-09T10:53:11+00:00"}, {"name": "kartik-v/yii2-date-range", "version": "v1.7.3", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-date-range.git", "reference": "7d80d6e2598e252487fe1acf911b8cb43f45e3b1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-date-range/zipball/7d80d6e2598e252487fe1acf911b8cb43f45e3b1", "reference": "7d80d6e2598e252487fe1acf911b8cb43f45e3b1", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=3.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.7.x-dev"}}, "autoload": {"psr-4": {"kartik\\daterange\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An advanced Yii 2 date range picker input for based on bootstrap-daterangepicker plugin.", "homepage": "https://github.com/kartik-v/yii2-date-range", "keywords": ["bootstrap", "bootstrap 3", "date", "date-range", "extension", "range", "time", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-date-range/issues", "source": "https://github.com/kartik-v/yii2-date-range/tree/v1.7.3"}, "time": "2021-09-01T12:16:39+00:00"}, {"name": "kartik-v/yii2-detail-view", "version": "v1.8.7", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-detail-view.git", "reference": "0b130581bce1ff26a98750433489cdc4816080a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-detail-view/zipball/0b130581bce1ff26a98750433489cdc4816080a1", "reference": "0b130581bce1ff26a98750433489cdc4816080a1", "shasum": ""}, "require": {"kartik-v/yii2-dialog": "~1.0", "kartik-v/yii2-krajee-base": ">=3.0.4", "kartik-v/yii2-widget-activeform": ">=1.6.2"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.8.x-dev"}}, "autoload": {"psr-4": {"kartik\\detail\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Yii 2 Detail View widget with special Bootstrap styles, ability to edit data, and more.", "homepage": "https://github.com/kartik-v/yii2-detail-view", "keywords": ["detail", "detail view", "extension", "form", "grid", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-detail-view/issues", "source": "https://github.com/kartik-v/yii2-detail-view/tree/v1.8.7"}, "time": "2022-03-04T09:42:29+00:00"}, {"name": "kartik-v/yii2-dialog", "version": "v1.0.6", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-dialog.git", "reference": "510c3a35ffe79987cde9a9366cedbff545fd92d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-dialog/zipball/510c3a35ffe79987cde9a9366cedbff545fd92d4", "reference": "510c3a35ffe79987cde9a9366cedbff545fd92d4", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=3.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\dialog\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An asset bundle for bootstrap3-dialog for Yii 2.0 framework.", "homepage": "https://github.com/kartik-v/yii2-dialog", "keywords": ["alert", "bootstrap", "dialog", "extension", "modal", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-dialog/issues", "source": "https://github.com/kartik-v/yii2-dialog/tree/v1.0.6"}, "time": "2021-09-02T08:26:37+00:00"}, {"name": "kartik-v/yii2-editable", "version": "v1.8.0", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-editable.git", "reference": "ae4cc2384e667ba9dfe8bfb0098716caeb2500a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-editable/zipball/ae4cc2384e667ba9dfe8bfb0098716caeb2500a8", "reference": "ae4cc2384e667ba9dfe8bfb0098716caeb2500a8", "shasum": ""}, "require": {"kartik-v/yii2-popover-x": "~1.3", "kartik-v/yii2-widget-activeform": ">=1.6.2"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.8.x-dev"}}, "autoload": {"psr-4": {"kartik\\editable\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An enhanced editable widget for Yii 2.0 that allows easy editing of displayed data with numerous configuration possibilities.", "homepage": "https://github.com/kartik-v/yii2-editable", "keywords": ["bootstrap", "editable", "input", "j<PERSON>y", "popover", "popover-x", "widget"], "support": {"issues": "https://github.com/kartik-v/yii2-editable/issues", "source": "https://github.com/kartik-v/yii2-editable/tree/v1.8.0"}, "time": "2022-04-29T12:51:01+00:00"}, {"name": "kartik-v/yii2-field-range", "version": "v1.3.5", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-field-range.git", "reference": "8a18edb343b3beb96ddc86e4a06aee28be160787"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-field-range/zipball/8a18edb343b3beb96ddc86e4a06aee28be160787", "reference": "8a18edb343b3beb96ddc86e4a06aee28be160787", "shasum": ""}, "require": {"kartik-v/yii2-helpers": ">=1.3.9", "kartik-v/yii2-widget-activeform": ">=1.5.7"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"kartik\\field\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Easily manage Yii 2 ActiveField ranges (from/to) with Bootstrap 3 addons markup and more", "homepage": "https://github.com/kartik-v/yii2-field-range", "keywords": ["addon", "bootstrap", "bootstrap 3", "bootstrap 4", "date", "extension", "field-range", "from", "range", "to", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-field-range/issues", "source": "https://github.com/kartik-v/yii2-field-range/tree/v1.3.5"}, "time": "2019-05-25T07:21:55+00:00"}, {"name": "kartik-v/yii2-grid", "version": "v3.5.3", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-grid.git", "reference": "cfc1a8e18bddfe22668783abb70f08583abab0a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-grid/zipball/cfc1a8e18bddfe22668783abb70f08583abab0a9", "reference": "cfc1a8e18bddfe22668783abb70f08583abab0a9", "shasum": ""}, "require": {"kartik-v/yii2-dialog": "~1.0", "kartik-v/yii2-krajee-base": ">=3.0.3"}, "suggest": {"kartik-v/yii2-bootstrap4-dropdown": "For enabling dropdown support when using with Bootstrap v4.x", "kartik-v/yii2-bootstrap5-dropdown": "For enabling dropdown support when using with Bootstrap v5.x", "kartik-v/yii2-mpdf": "For exporting grids to PDF"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "3.5.x-dev"}}, "autoload": {"psr-4": {"kartik\\grid\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Yii 2 GridView on steroids. Various enhancements and utilities for the Yii 2.0 GridView widget.", "homepage": "https://github.com/kartik-v/yii2-grid", "keywords": ["extension", "grid", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-grid/issues", "source": "https://github.com/kartik-v/yii2-grid/tree/v3.5.3"}, "funding": [{"url": "https://opencollective.com/yii2-grid", "type": "open_collective"}], "time": "2023-07-25T11:41:33+00:00"}, {"name": "kartik-v/yii2-helpers", "version": "v1.3.9", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-helpers.git", "reference": "0bec7a97bf9d0293d96be5c496fe9654c4dec94b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-helpers/zipball/0bec7a97bf9d0293d96be5c496fe9654c4dec94b", "reference": "0bec7a97bf9d0293d96be5c496fe9654c4dec94b", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=2.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"kartik\\helpers\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A collection of useful helper functions for Yii Framework 2.0", "homepage": "https://github.com/kartik-v/yii2-helpers", "keywords": ["bootstrap", "extension", "helper", "utilities", "yii", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-helpers/issues", "source": "https://github.com/kartik-v/yii2-helpers/tree/master"}, "time": "2018-10-09T08:03:44+00:00"}, {"name": "kartik-v/yii2-krajee-base", "version": "v3.0.5", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-krajee-base.git", "reference": "5c095126d1be47e0bb1f92779b7dc099f6feae31"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-krajee-base/zipball/5c095126d1be47e0bb1f92779b7dc099f6feae31", "reference": "5c095126d1be47e0bb1f92779b7dc099f6feae31", "shasum": ""}, "suggest": {"yiisoft/yii2-bootstrap": "for Krajee extensions to work with Bootstrap 3.x version", "yiisoft/yii2-bootstrap4": "for Krajee extensions to work with Bootstrap 4.x version", "yiisoft/yii2-bootstrap5": "for Krajee extensions to work with Bootstrap 5.x version"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\base\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Base library and foundation components for all Yii2 Krajee extensions.", "homepage": "https://github.com/kartik-v/yii2-krajee-base", "keywords": ["base", "extension", "foundation", "krajee", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-krajee-base/issues", "source": "https://github.com/kartik-v/yii2-krajee-base/tree/v3.0.5"}, "time": "2022-06-01T14:05:39+00:00"}, {"name": "kartik-v/yii2-popover-x", "version": "v1.3.5", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-popover-x.git", "reference": "b0320d1315bbfce31ec8907882c6f4abed223a28"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-popover-x/zipball/b0320d1315bbfce31ec8907882c6f4abed223a28", "reference": "b0320d1315bbfce31ec8907882c6f4abed223a28", "shasum": ""}, "require": {"kartik-v/bootstrap-popover-x": ">=1.4", "kartik-v/yii2-krajee-base": ">=2.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"kartik\\popover\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An extended bootstrap 3.0 popover widget which combines both the bootstrap popover and modal features and includes various new styling enhancements.", "homepage": "https://github.com/kartik-v/yii2-popover-x", "keywords": ["bootstrap", "extended", "j<PERSON>y", "modal", "modal-popover", "popover", "popover-x"], "support": {"issues": "https://github.com/kartik-v/yii2-popover-x/issues", "source": "https://github.com/kartik-v/yii2-popover-x/tree/master"}, "time": "2020-04-02T17:20:29+00:00"}, {"name": "kartik-v/yii2-tabs-x", "version": "v1.2.9", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-tabs-x.git", "reference": "20e2a2b41ca43e09574caab408004e5ac4e00a7d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-tabs-x/zipball/20e2a2b41ca43e09574caab408004e5ac4e00a7d", "reference": "20e2a2b41ca43e09574caab408004e5ac4e00a7d", "shasum": ""}, "require": {"kartik-v/bootstrap-tabs-x": "~1.3", "kartik-v/yii2-krajee-base": ">=3.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"kartik\\tabs\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A supercharged Bootstrap tabs widget with ability to align tabs in multiple ways, add borders, rotated titles, and more.", "homepage": "https://github.com/kartik-v/yii2-tabs-x", "keywords": ["bootstrap", "extended", "j<PERSON>y", "modal", "modal-tabs", "tabs", "tabs-x"], "support": {"issues": "https://github.com/kartik-v/yii2-tabs-x/issues", "source": "https://github.com/kartik-v/yii2-tabs-x/tree/v1.2.9"}, "time": "2022-10-15T11:14:01+00:00"}, {"name": "kartik-v/yii2-widget-activeform", "version": "v1.6.2", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-activeform.git", "reference": "98dbf789c9f71a35c76a8c2b667e86815ae51ac1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-activeform/zipball/98dbf789c9f71a35c76a8c2b667e86815ae51ac1", "reference": "98dbf789c9f71a35c76a8c2b667e86815ae51ac1", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=3.0.3"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}}, "autoload": {"psr-4": {"kartik\\form\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Yii2 active-form and active-field with full bootstrap styling support (sub repo split from yii2-widgets).", "homepage": "https://github.com/kartik-v/yii2-widget-activeform", "keywords": ["activefield", "activeform", "extension", "field", "form", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-activeform/issues", "source": "https://github.com/kartik-v/yii2-widget-activeform/tree/v1.6.2"}, "funding": [{"url": "https://opencollective.com/yii2-widgets", "type": "open_collective"}], "time": "2022-02-26T18:53:51+00:00"}, {"name": "kartik-v/yii2-widget-affix", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-affix.git", "reference": "2184119bfa518c285406156f744769b13b861712"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-affix/zipball/2184119bfa518c285406156f744769b13b861712", "reference": "2184119bfa518c285406156f744769b13b861712", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*"}, "type": "yii2-extension", "autoload": {"psr-4": {"kartik\\affix\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD 3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A scrollspy and affixed enhanced navigation to highlight page sections (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-affix", "keywords": ["affix", "bootstrap", "extension", "j<PERSON>y", "navigation", "plugin", "scrollspy", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-affix/issues", "source": "https://github.com/kartik-v/yii2-widget-affix/tree/master"}, "time": "2014-11-09T04:56:27+00:00"}, {"name": "kartik-v/yii2-widget-alert", "version": "v1.1.5", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-alert.git", "reference": "6a45d7dc294eecd578cf8cb9acb671d1cafa0727"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-alert/zipball/6a45d7dc294eecd578cf8cb9acb671d1cafa0727", "reference": "6a45d7dc294eecd578cf8cb9acb671d1cafa0727", "shasum": ""}, "require": {"kartik-v/yii2-widget-growl": ">=1.1.2"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"kartik\\alert\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A widget to generate alert based notifications using bootstrap-alert plugin (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-alert", "keywords": ["alert", "block", "bootstrap", "extension", "flash", "j<PERSON>y", "notification", "plugin", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-alert/issues", "source": "https://github.com/kartik-v/yii2-widget-alert/tree/v1.1.5"}, "time": "2021-10-16T10:23:22+00:00"}, {"name": "kartik-v/yii2-widget-colorinput", "version": "v1.0.6", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-colorinput.git", "reference": "e35e6c7615a735b65557d6c38d112b77e2628c69"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-colorinput/zipball/e35e6c7615a735b65557d6c38d112b77e2628c69", "reference": "e35e6c7615a735b65557d6c38d112b77e2628c69", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=1.9"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\color\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An enhanced Yii 2 widget encapsulating the HTML 5 color input (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-colorinput", "keywords": ["HTML5", "color", "extension", "form", "input", "j<PERSON>y", "plugin", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-colorinput/issues", "source": "https://github.com/kartik-v/yii2-widget-colorinput/tree/v1.0.6"}, "time": "2020-10-23T17:50:44+00:00"}, {"name": "kartik-v/yii2-widget-datepicker", "version": "v1.4.8", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-datepicker.git", "reference": "f5f8b396cf03d4a383aad5e7b338f8cb065abf66"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-datepicker/zipball/f5f8b396cf03d4a383aad5e7b338f8cb065abf66", "reference": "f5f8b396cf03d4a383aad5e7b338f8cb065abf66", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=2.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-4": {"kartik\\date\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Yii2 wrapper for the bootstrap datepicker plugin (sub repo split from yii2-widgets).", "homepage": "https://github.com/kartik-v/yii2-widget-datepicker", "keywords": ["date", "extension", "form", "j<PERSON>y", "picker", "plugin", "select2", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-datepicker/issues", "source": "https://github.com/kartik-v/yii2-widget-datepicker/tree/v1.4.8"}, "time": "2021-10-28T03:58:09+00:00"}, {"name": "kartik-v/yii2-widget-datetimepicker", "version": "v1.5.1", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-datetimepicker.git", "reference": "85b22d38553ca207f86be198f37e6531347e9a23"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-datetimepicker/zipball/85b22d38553ca207f86be198f37e6531347e9a23", "reference": "85b22d38553ca207f86be198f37e6531347e9a23", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=3.0.4"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}}, "autoload": {"psr-4": {"kartik\\datetime\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Yii2 wrapper for the bootstrap datetimepicker plugin (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-datetimepicker", "keywords": ["datetime", "extension", "form", "j<PERSON>y", "picker", "plugin", "select2", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-datetimepicker/issues", "source": "https://github.com/kartik-v/yii2-widget-datetimepicker/tree/v1.5.1"}, "time": "2022-03-18T17:42:22+00:00"}, {"name": "kartik-v/yii2-widget-depdrop", "version": "v1.0.6", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-depdrop.git", "reference": "ea347e3793fbd8273cc9bd1eb94c4b32bb55d318"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-depdrop/zipball/ea347e3793fbd8273cc9bd1eb94c4b32bb55d318", "reference": "ea347e3793fbd8273cc9bd1eb94c4b32bb55d318", "shasum": ""}, "require": {"kartik-v/dependent-dropdown": "~1.4", "kartik-v/yii2-krajee-base": ">=2.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\depdrop\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Widget that enables setting up dependent dropdowns with nested dependencies (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-depdrop", "keywords": ["dependent", "dropdown", "extension", "form", "j<PERSON>y", "plugin", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-depdrop/issues", "source": "https://github.com/kartik-v/yii2-widget-depdrop/tree/v1.0.6"}, "time": "2019-04-19T07:02:48+00:00"}, {"name": "kartik-v/yii2-widget-fileinput", "version": "v1.1.1", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-fileinput.git", "reference": "b5500b6855526837154694c2afab8dbc3afc8abd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-fileinput/zipball/b5500b6855526837154694c2afab8dbc3afc8abd", "reference": "b5500b6855526837154694c2afab8dbc3afc8abd", "shasum": ""}, "require": {"kartik-v/bootstrap-fileinput": ">=5.5.0", "kartik-v/yii2-krajee-base": ">=3.0.5"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"kartik\\file\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An enhanced FileInput widget for Bootstrap 3.x, 4.x & 5.x with file preview, multiple selection, and more features (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-fileinput", "keywords": ["extension", "file", "form", "input", "j<PERSON>y", "plugin", "upload", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-fileinput/issues", "source": "https://github.com/kartik-v/yii2-widget-fileinput/tree/v1.1.1"}, "funding": [{"url": "https://opencollective.com/yii2-widget-fileinput", "type": "open_collective"}], "time": "2022-06-28T04:31:04+00:00"}, {"name": "kartik-v/yii2-widget-growl", "version": "v1.1.2", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-growl.git", "reference": "37e8f9f10d3bc9d71f3ef64c4aaa0e2fc83dd5dc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-growl/zipball/37e8f9f10d3bc9d71f3ef64c4aaa0e2fc83dd5dc", "reference": "37e8f9f10d3bc9d71f3ef64c4aaa0e2fc83dd5dc", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=2.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"kartik\\growl\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A widget to generate growl based notifications using bootstrap-growl plugin (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-growl", "keywords": ["alert", "bootstrap", "extension", "growl", "j<PERSON>y", "notification", "plugin", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-growl/issues", "source": "https://github.com/kartik-v/yii2-widget-growl/tree/v1.1.2"}, "time": "2021-05-19T12:44:49+00:00"}, {"name": "kartik-v/yii2-widget-rangeinput", "version": "v1.0.2", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-rangeinput.git", "reference": "dd9019bab7e5bf570a02870d9e74387891bbdb32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-rangeinput/zipball/dd9019bab7e5bf570a02870d9e74387891bbdb32", "reference": "dd9019bab7e5bf570a02870d9e74387891bbdb32", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=1.9"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\range\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An enhanced Yii 2 widget encapsulating the HTML 5 range input (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-rangeinput", "keywords": ["HTML5", "extension", "form", "input", "j<PERSON>y", "plugin", "range", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-rangeinput/issues", "source": "https://github.com/kartik-v/yii2-widget-rangeinput/tree/master"}, "time": "2018-09-07T10:05:08+00:00"}, {"name": "kartik-v/yii2-widget-rating", "version": "v1.0.5", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-rating.git", "reference": "d3d7249490044f80e65f8f3938191f39a76586b2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-rating/zipball/d3d7249490044f80e65f8f3938191f39a76586b2", "reference": "d3d7249490044f80e65f8f3938191f39a76586b2", "shasum": ""}, "require": {"kartik-v/bootstrap-star-rating": "~4.0", "kartik-v/yii2-krajee-base": ">=1.9"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\rating\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A Yii2 widget for the simple yet powerful bootstrap-star-rating plugin with fractional rating support (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-rating", "keywords": ["Rating", "bootstrap", "extension", "form", "input", "j<PERSON>y", "plugin", "star", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-rating/issues", "source": "https://github.com/kartik-v/yii2-widget-rating/tree/v1.0.5"}, "time": "2021-11-20T05:26:05+00:00"}, {"name": "kartik-v/yii2-widget-select2", "version": "v2.2.5", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-select2.git", "reference": "4b8ef7dd9780531fc997fa23a53a38a1f7674bec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-select2/zipball/4b8ef7dd9780531fc997fa23a53a38a1f7674bec", "reference": "4b8ef7dd9780531fc997fa23a53a38a1f7674bec", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=3.0.4", "select2/select2": ">=4.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}}, "autoload": {"psr-4": {"kartik\\select2\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Yii2 wrapper for the Select2 jQuery plugin (sub repo split from yii2-widgets).", "homepage": "https://github.com/kartik-v/yii2-widget-select2", "keywords": ["dropdown", "extension", "form", "j<PERSON>y", "plugin", "select2", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-select2/issues", "source": "https://github.com/kartik-v/yii2-widget-select2/tree/v2.2.5"}, "funding": [{"url": "https://opencollective.com/yii2-widget-select2", "type": "open_collective"}], "time": "2023-06-22T07:43:31+00:00"}, {"name": "kartik-v/yii2-widget-sidenav", "version": "v1.0.1", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-sidenav.git", "reference": "87e9c815624aa966d70bb4507b3d53c158db0d43"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-sidenav/zipball/87e9c815624aa966d70bb4507b3d53c158db0d43", "reference": "87e9c815624aa966d70bb4507b3d53c158db0d43", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\sidenav\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An enhanced side navigation menu styled for bootstrap (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-sidenav", "keywords": ["bootstrap", "extension", "j<PERSON>y", "menu", "navigation", "plugin", "sidenav", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-sidenav/issues", "source": "https://github.com/kartik-v/yii2-widget-sidenav/tree/v1.0.1"}, "funding": [{"url": "https://opencollective.com/yii2-widget-sidenav", "type": "open_collective"}], "time": "2021-04-08T17:49:26+00:00"}, {"name": "kartik-v/yii2-widget-spinner", "version": "v1.0.1", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-spinner.git", "reference": "eb10dad17a107bf14f173c99994770ca23c548a6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-spinner/zipball/eb10dad17a107bf14f173c99994770ca23c548a6", "reference": "eb10dad17a107bf14f173c99994770ca23c548a6", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=2.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\spinner\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A widget to render animated CSS3 loading spinners with VML fallback for IE (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-spinner", "keywords": ["CSS3", "extension", "j<PERSON>y", "loading", "plugin", "spinner", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-spinner/issues", "source": "https://github.com/kartik-v/yii2-widget-spinner/tree/master"}, "time": "2018-10-09T11:54:03+00:00"}, {"name": "kartik-v/yii2-widget-switchinput", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-switchinput.git", "reference": "7d8ee999d79bcdc1601da5cd59439ac7eb1f5ea6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-switchinput/zipball/7d8ee999d79bcdc1601da5cd59439ac7eb1f5ea6", "reference": "7d8ee999d79bcdc1601da5cd59439ac7eb1f5ea6", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*"}, "type": "yii2-extension", "autoload": {"psr-4": {"kartik\\switchinput\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD 3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A Yii2 wrapper widget for the Bootstrap Switch plugin to use checkboxes & radios as toggle switchinputes (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-switchinput", "keywords": ["bootstrap", "extension", "form", "input", "j<PERSON>y", "plugin", "switchinput", "toggle", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-switchinput/issues", "source": "https://github.com/kartik-v/yii2-widget-switchinput/tree/master"}, "time": "2016-01-10T16:47:35+00:00"}, {"name": "kartik-v/yii2-widget-timepicker", "version": "v1.0.5", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-timepicker.git", "reference": "680aec2d79846e926c072da455cf6f33e1c3bb12"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-timepicker/zipball/680aec2d79846e926c072da455cf6f33e1c3bb12", "reference": "680aec2d79846e926c072da455cf6f33e1c3bb12", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=2.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\time\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Yii2 wrapper for the bootstrap timepicker plugin (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-timepicker", "keywords": ["bootstrap", "extension", "form", "j<PERSON>y", "picker", "plugin", "time", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-timepicker/issues", "source": "https://github.com/kartik-v/yii2-widget-timepicker/tree/v1.0.5"}, "time": "2021-10-28T03:49:56+00:00"}, {"name": "kartik-v/yii2-widget-touchspin", "version": "v1.2.4", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-touchspin.git", "reference": "1eec4c3f3a8bf9a170e1e0682c2c89f2929d65e9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-touchspin/zipball/1eec4c3f3a8bf9a170e1e0682c2c89f2929d65e9", "reference": "1eec4c3f3a8bf9a170e1e0682c2c89f2929d65e9", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=3.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"kartik\\touchspin\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A Yii2 wrapper widget for the Bootstrap Switch plugin to use checkboxes & radios as toggle touchspines (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-touchspin", "keywords": ["bootstrap", "extension", "form", "input", "j<PERSON>y", "plugin", "spinner", "touch", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-touchspin/issues", "source": "https://github.com/kartik-v/yii2-widget-touchspin/tree/v1.2.4"}, "time": "2021-09-02T12:50:50+00:00"}, {"name": "kartik-v/yii2-widget-typeahead", "version": "v1.0.4", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-typeahead.git", "reference": "7b7041a3cbbeb2db0a608e9f6c9b3f4f63b0069d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-typeahead/zipball/7b7041a3cbbeb2db0a608e9f6c9b3f4f63b0069d", "reference": "7b7041a3cbbeb2db0a608e9f6c9b3f4f63b0069d", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=2.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\typeahead\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Yii2 wrapper for the Twitter Typeahead plugin (sub repo split from yii2-widgets).", "homepage": "https://github.com/kartik-v/yii2-widget-typeahead", "keywords": ["dropdown", "extension", "form", "j<PERSON>y", "plugin", "typeahead", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-typeahead/issues", "source": "https://github.com/kartik-v/yii2-widget-typeahead/tree/master"}, "time": "2019-05-29T12:06:56+00:00"}, {"name": "kartik-v/yii2-widgets", "version": "v3.4.1", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widgets.git", "reference": "e5a030d700243a90eccf96a070380bd3b76e17a3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widgets/zipball/e5a030d700243a90eccf96a070380bd3b76e17a3", "reference": "e5a030d700243a90eccf96a070380bd3b76e17a3", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*", "kartik-v/yii2-widget-activeform": "*", "kartik-v/yii2-widget-affix": "*", "kartik-v/yii2-widget-alert": "*", "kartik-v/yii2-widget-colorinput": "*", "kartik-v/yii2-widget-datepicker": "*", "kartik-v/yii2-widget-datetimepicker": "*", "kartik-v/yii2-widget-depdrop": "*", "kartik-v/yii2-widget-fileinput": "*", "kartik-v/yii2-widget-growl": "*", "kartik-v/yii2-widget-rangeinput": "*", "kartik-v/yii2-widget-rating": "*", "kartik-v/yii2-widget-select2": "*", "kartik-v/yii2-widget-sidenav": "*", "kartik-v/yii2-widget-spinner": "*", "kartik-v/yii2-widget-switchinput": "*", "kartik-v/yii2-widget-timepicker": "*", "kartik-v/yii2-widget-touchspin": "*", "kartik-v/yii2-widget-typeahead": "*"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\widgets\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Collection of useful widgets for Yii Framework 2.0 extending functionalities for Bootstrap", "homepage": "https://github.com/kartik-v/yii2-widgets", "keywords": ["extension", "form", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widgets/issues", "source": "https://github.com/kartik-v/yii2-widgets/tree/master"}, "time": "2018-10-09T17:40:19+00:00"}, {"name": "kuainiu/yii2-kuainiu", "version": "2.1.1", "source": {"type": "git", "url": "************************:xlerr/yii2-kuainiu.git", "reference": "e021bceec78ee77b79bc6d60143930e711ef5306"}, "dist": {"type": "zip", "url": "https://biz-satis-test.kuainiujinke.com/composer/dist/kuainiu/yii2-kuainiu/kuainiu-yii2-kuainiu-2.1.1-a04969.zip", "reference": "e021bceec78ee77b79bc6d60143930e711ef5306"}, "require": {"php": ">=7.1", "yiisoft/yii2-authclient": "^2.0"}, "type": "yii2-extension", "autoload": {"psr-4": {"kuainiu\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "kuainiu.io extenion for using via yii2-authclient", "keywords": ["authclient", "kuainiu", "o<PERSON>h", "yii2"], "time": "2022-04-11T02:27:48+00:00"}, {"name": "league/flysystem", "version": "2.5.0", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem.git", "reference": "8aaffb653c5777781b0f7f69a5d937baf7ab6cdb"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/league/flysystem/2.5.0/league-flysystem-2.5.0.zip", "reference": "8aaffb653c5777781b0f7f69a5d937baf7ab6cdb", "shasum": ""}, "require": {"ext-json": "*", "league/mime-type-detection": "^1.0.0", "php": "^7.2 || ^8.0"}, "conflict": {"guzzlehttp/ringphp": "<1.1.1"}, "require-dev": {"async-aws/s3": "^1.5", "async-aws/simple-s3": "^1.0", "aws/aws-sdk-php": "^3.132.4", "composer/semver": "^3.0", "ext-fileinfo": "*", "ext-ftp": "*", "friendsofphp/php-cs-fixer": "^3.2", "google/cloud-storage": "^1.23", "phpseclib/phpseclib": "^2.0", "phpstan/phpstan": "^0.12.26", "phpunit/phpunit": "^8.5 || ^9.4", "sabre/dav": "^4.1"}, "type": "library", "autoload": {"psr-4": {"League\\Flysystem\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "File storage abstraction for PHP", "keywords": ["WebDAV", "aws", "cloud", "file", "files", "filesystem", "filesystems", "ftp", "s3", "sftp", "storage"], "funding": [{"url": "https://ecologi.com/frankdejonge", "type": "custom"}, {"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "time": "2022-09-17T21:02:32+00:00"}, {"name": "league/flysystem-sftp-v3", "version": "2.5.0", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem-sftp-v3.git", "reference": "7b8ab36804f942b6d4331b460d8ba38594d26e8b"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/league/flysystem-sftp-v3/2.5.0/league-flysystem-sftp-v3-2.5.0.zip", "reference": "7b8ab36804f942b6d4331b460d8ba38594d26e8b", "shasum": ""}, "require": {"league/flysystem": "^2.0.0", "league/mime-type-detection": "^1.0.0", "php": "^7.2 || ^8.0", "phpseclib/phpseclib": "^3.0"}, "type": "library", "autoload": {"psr-4": {"League\\Flysystem\\PhpseclibV3\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "SFTP filesystem adapter for Flysystem.", "keywords": ["Flysystem", "file", "files", "filesystem", "sftp"], "funding": [{"url": "https://ecologi.com/frankdejonge", "type": "custom"}, {"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "time": "2022-04-27T17:27:27+00:00"}, {"name": "league/mime-type-detection", "version": "1.15.0", "source": {"type": "git", "url": "https://github.com/thephpleague/mime-type-detection.git", "reference": "ce0f4d1e8a6f4eb0ddff33f57c69c50fd09f4301"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/league/mime-type-detection/1.15.0/league-mime-type-detection-1.15.0.zip", "reference": "ce0f4d1e8a6f4eb0ddff33f57c69c50fd09f4301", "shasum": ""}, "require": {"ext-fileinfo": "*", "php": "^7.4 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "phpstan/phpstan": "^0.12.68", "phpunit/phpunit": "^8.5.8 || ^9.3 || ^10.0"}, "type": "library", "autoload": {"psr-4": {"League\\MimeTypeDetection\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Mime-type detection for Flysystem", "funding": [{"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "time": "2024-01-28T23:22:08+00:00"}, {"name": "league/oauth2-client", "version": "2.8.1", "source": {"type": "git", "url": "https://github.com/thephpleague/oauth2-client.git", "reference": "9df2924ca644736c835fc60466a3a60390d334f9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/oauth2-client/zipball/9df2924ca644736c835fc60466a3a60390d334f9", "reference": "9df2924ca644736c835fc60466a3a60390d334f9", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/guzzle": "^6.5.8 || ^7.4.5", "php": "^7.1 || >=8.0.0 <8.5.0"}, "require-dev": {"mockery/mockery": "^1.3.5", "php-parallel-lint/php-parallel-lint": "^1.4", "phpunit/phpunit": "^7 || ^8 || ^9 || ^10 || ^11", "squizlabs/php_codesniffer": "^3.11"}, "type": "library", "autoload": {"psr-4": {"League\\OAuth2\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.alexbilbie.com", "role": "Developer"}, {"name": "<PERSON>", "homepage": "https://github.com/shadowhand", "role": "Contributor"}], "description": "OAuth 2.0 Client Library", "keywords": ["Authentication", "SSO", "authorization", "identity", "idp", "o<PERSON>h", "oauth2", "single sign on"], "support": {"issues": "https://github.com/thephpleague/oauth2-client/issues", "source": "https://github.com/thephpleague/oauth2-client/tree/2.8.1"}, "time": "2025-02-26T04:37:30+00:00"}, {"name": "maennchen/zipstream-php", "version": "2.2.6", "source": {"type": "git", "url": "https://github.com/maennchen/ZipStream-PHP.git", "reference": "30ad6f93cf3efe4192bc7a4c9cad11ff8f4f237f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maennchen/ZipStream-PHP/zipball/30ad6f93cf3efe4192bc7a4c9cad11ff8f4f237f", "reference": "30ad6f93cf3efe4192bc7a4c9cad11ff8f4f237f", "shasum": ""}, "require": {"myclabs/php-enum": "^1.5", "php": "^7.4 || ^8.0", "psr/http-message": "^1.0", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"ext-zip": "*", "friendsofphp/php-cs-fixer": "^3.9", "guzzlehttp/guzzle": "^6.5.3 || ^7.2.0", "mikey179/vfsstream": "^1.6", "php-coveralls/php-coveralls": "^2.4", "phpunit/phpunit": "^8.5.8 || ^9.4.2", "vimeo/psalm": "^4.1"}, "type": "library", "autoload": {"psr-4": {"ZipStream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Jonatan Männchen", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "ZipStream is a library for dynamically streaming dynamic zip files from PHP without writing to the disk at all on the server.", "keywords": ["stream", "zip"], "support": {"issues": "https://github.com/maennchen/ZipStream-PHP/issues", "source": "https://github.com/maennchen/ZipStream-PHP/tree/2.2.6"}, "funding": [{"url": "https://github.com/maennchen", "type": "github"}, {"url": "https://opencollective.com/zipstream", "type": "open_collective"}], "time": "2022-11-25T18:57:19+00:00"}, {"name": "markbaker/complex", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPComplex.git", "reference": "95c56caa1cf5c766ad6d65b6344b807c1e8405b9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPComplex/zipball/95c56caa1cf5c766ad6d65b6344b807c1e8405b9", "reference": "95c56caa1cf5c766ad6d65b6344b807c1e8405b9", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "^9.3", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "squizlabs/php_codesniffer": "^3.7"}, "type": "library", "autoload": {"psr-4": {"Complex\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "support": {"issues": "https://github.com/MarkBaker/PHPComplex/issues", "source": "https://github.com/MarkBaker/PHPComplex/tree/3.0.2"}, "time": "2022-12-06T16:21:08+00:00"}, {"name": "markbaker/matrix", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPMatrix.git", "reference": "728434227fe21be27ff6d86621a1b13107a2562c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPMatrix/zipball/728434227fe21be27ff6d86621a1b13107a2562c", "reference": "728434227fe21be27ff6d86621a1b13107a2562c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "^9.3", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "^4.0", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "sebastian/phpcpd": "^4.0", "squizlabs/php_codesniffer": "^3.7"}, "type": "library", "autoload": {"psr-4": {"Matrix\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "support": {"issues": "https://github.com/MarkBaker/PHPMatrix/issues", "source": "https://github.com/MarkBaker/PHPMatrix/tree/3.0.1"}, "time": "2022-12-02T22:17:43+00:00"}, {"name": "mdmsoft/yii2-admin", "version": "2.13", "source": {"type": "git", "url": "************************:xlerr/yii2-admin.git", "reference": "7820f47f31c1a94f2b12864410bdadbdbdd168fc"}, "dist": {"type": "zip", "url": "https://biz-satis-test.kuainiujinke.com/composer/dist/mdmsoft/yii2-admin/mdmsoft-yii2-admin-2.13-632c66.zip", "reference": "7820f47f31c1a94f2b12864410bdadbdbdd168fc"}, "require": {"yiisoft/yii2": "~2.0.7"}, "require-dev": {"yiisoft/yii2-codeception": "~2.0"}, "suggest": {"yiisoft/yii2-bootstrap": "Used when using layout 'left-menu', 'right-menu' or 'top-menu'"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-1.0.master": "1.0.x-dev", "dev-master": "2.x-dev"}, "asset-installer-paths": {"npm-asset-library": "vendor/npm", "bower-asset-library": "vendor/bower"}}, "autoload": {"psr-4": {"mdm\\admin\\": ""}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "Misbah<PERSON>", "email": "<EMAIL>"}], "description": "RBAC Auth manager for Yii2 ", "keywords": ["admin", "auth", "rbac", "yii"], "support": {"issues": "https://github.com/mdmsoft/yii2-admin/issues", "source": "https://github.com/mdmsoft/yii2-admin"}, "time": "2022-07-14T09:56:18+00:00"}, {"name": "myclabs/php-enum", "version": "1.8.5", "source": {"type": "git", "url": "https://github.com/myclabs/php-enum.git", "reference": "e7be26966b7398204a234f8673fdad5ac6277802"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/php-enum/zipball/e7be26966b7398204a234f8673fdad5ac6277802", "reference": "e7be26966b7398204a234f8673fdad5ac6277802", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.3 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^9.5", "squizlabs/php_codesniffer": "1.*", "vimeo/psalm": "^4.6.2 || ^5.2"}, "type": "library", "autoload": {"psr-4": {"MyCLabs\\Enum\\": "src/"}, "classmap": ["stubs/Stringable.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP Enum contributors", "homepage": "https://github.com/myclabs/php-enum/graphs/contributors"}], "description": "PHP Enum implementation", "homepage": "https://github.com/myclabs/php-enum", "keywords": ["enum"], "support": {"issues": "https://github.com/myclabs/php-enum/issues", "source": "https://github.com/myclabs/php-enum/tree/1.8.5"}, "funding": [{"url": "https://github.com/mnapoli", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/myclabs/php-enum", "type": "tidelift"}], "time": "2025-01-14T11:49:03+00:00"}, {"name": "nesbot/carbon", "version": "2.73.0", "source": {"type": "git", "url": "https://github.com/CarbonPHP/carbon.git", "reference": "9228ce90e1035ff2f0db84b40ec2e023ed802075"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/9228ce90e1035ff2f0db84b40ec2e023ed802075", "reference": "9228ce90e1035ff2f0db84b40ec2e023ed802075", "shasum": ""}, "require": {"carbonphp/carbon-doctrine-types": "*", "ext-json": "*", "php": "^7.1.8 || ^8.0", "psr/clock": "^1.0", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation": "^3.4 || ^4.0 || ^5.0 || ^6.0"}, "provide": {"psr/clock-implementation": "1.0"}, "require-dev": {"doctrine/dbal": "^2.0 || ^3.1.4 || ^4.0", "doctrine/orm": "^2.7 || ^3.0", "friendsofphp/php-cs-fixer": "^3.0", "kylekatarnls/multi-tester": "^2.0", "ondrejmirtes/better-reflection": "<6", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.99 || ^1.7.14", "phpunit/php-file-iterator": "^2.0.5 || ^3.0.6", "phpunit/phpunit": "^7.5.20 || ^8.5.26 || ^9.5.20", "squizlabs/php_codesniffer": "^3.4"}, "bin": ["bin/carbon"], "type": "library", "extra": {"laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}, "branch-alias": {"dev-2.x": "2.x-dev", "dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://markido.com"}, {"name": "kylekatarnls", "homepage": "https://github.com/kylekatarnls"}], "description": "An API extension for DateTime that supports 281 different languages.", "homepage": "https://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "support": {"docs": "https://carbon.nesbot.com/docs", "issues": "https://github.com/briannesbitt/Carbon/issues", "source": "https://github.com/briannesbitt/Carbon"}, "funding": [{"url": "https://github.com/sponsors/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon#sponsor", "type": "opencollective"}, {"url": "https://tidelift.com/subscription/pkg/packagist-nesbot-carbon?utm_source=packagist-nesbot-carbon&utm_medium=referral&utm_campaign=readme", "type": "tidelift"}], "time": "2025-01-08T20:10:23+00:00"}, {"name": "notamedia/yii2-sentry", "version": "1.7.0", "source": {"type": "git", "url": "https://github.com/notamedia/yii2-sentry.git", "reference": "24c1be71f2f9193c3c62b2def3fc4b32b3276c1b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/notamedia/yii2-sentry/zipball/24c1be71f2f9193c3c62b2def3fc4b32b3276c1b", "reference": "24c1be71f2f9193c3c62b2def3fc4b32b3276c1b", "shasum": ""}, "require": {"php": "^7.2|^8.0", "sentry/sdk": "^3.0", "yiisoft/yii2": "^2.0"}, "require-dev": {"codeception/codeception": "^4.0", "codeception/module-asserts": "^1.3", "codeception/module-yii2": "^1.1"}, "type": "yii2-extension", "extra": {"asset-vcs-driver-options": {"github-no-api": true}, "asset-pattern-skip-version": "(-build|-patch)"}, "autoload": {"psr-4": {"notamedia\\sentry\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Yii2 logger for Sentry", "keywords": ["sentry", "yii2"], "support": {"issues": "https://github.com/notamedia/yii2-sentry/issues", "source": "https://github.com/notamedia/yii2-sentry/tree/1.7.0"}, "time": "2021-01-12T15:17:45+00:00"}, {"name": "npm-asset/ace-builds", "version": "1.4.14", "dist": {"type": "tar", "url": "https://registry.npmjs.org/ace-builds/-/ace-builds-1.4.14.tgz"}, "type": "npm-asset", "license": ["BSD-3-<PERSON><PERSON>"]}, {"name": "npm-asset/babel--helper-string-parser", "version": "7.27.1", "dist": {"type": "tar", "url": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "npm-asset/babel--helper-validator-identifier", "version": "7.27.1", "dist": {"type": "tar", "url": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "npm-asset/babel--parser", "version": "7.28.0", "dist": {"type": "tar", "url": "https://registry.npmjs.org/@babel/parser/-/parser-7.28.0.tgz"}, "require": {"npm-asset/babel--types": ">=7.28.0,<8.0.0"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "npm-asset/babel--types", "version": "7.28.1", "dist": {"type": "tar", "url": "https://registry.npmjs.org/@babel/types/-/types-7.28.1.tgz"}, "require": {"npm-asset/babel--helper-string-parser": ">=7.27.1,<8.0.0", "npm-asset/babel--helper-validator-identifier": ">=7.27.1,<8.0.0"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "npm-asset/commander", "version": "9.5.0", "dist": {"type": "tar", "url": "https://registry.npmjs.org/commander/-/commander-9.5.0.tgz"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "npm-asset/csstype", "version": "3.1.3", "dist": {"type": "tar", "url": "https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "npm-asset/entities", "version": "4.5.0", "dist": {"type": "tar", "url": "https://registry.npmjs.org/entities/-/entities-4.5.0.tgz"}, "type": "npm-asset", "license": ["BSD-2-<PERSON><PERSON>"]}, {"name": "npm-asset/estree-walker", "version": "2.0.2", "dist": {"type": "tar", "url": "https://registry.npmjs.org/estree-walker/-/estree-walker-2.0.2.tgz"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "npm-asset/highlightjs--cdn-assets", "version": "11.11.1", "dist": {"type": "tar", "url": "https://registry.npmjs.org/@highlightjs/cdn-assets/-/cdn-assets-11.11.1.tgz"}, "type": "npm-asset", "license": ["BSD-3-<PERSON><PERSON>"]}, {"name": "npm-asset/jridgewell--sourcemap-codec", "version": "1.5.0", "dist": {"type": "tar", "url": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "npm-asset/lodash", "version": "4.17.21-patch1", "source": {"type": "git", "url": "https://github.com/lodash/lodash.git", "reference": "c6e281b878b315c7a10d90f9c2af4cdb112d9625"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lodash/lodash/zipball/c6e281b878b315c7a10d90f9c2af4cdb112d9625", "reference": "c6e281b878b315c7a10d90f9c2af4cdb112d9625"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "npm-asset/magic-string", "version": "0.30.17", "dist": {"type": "tar", "url": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.17.tgz"}, "require": {"npm-asset/jridgewell--sourcemap-codec": ">=1.5.0,<2.0.0"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "npm-asset/moment", "version": "2.30.1", "dist": {"type": "tar", "url": "https://registry.npmjs.org/moment/-/moment-2.30.1.tgz"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "npm-asset/nanoid", "version": "3.3.11", "dist": {"type": "tar", "url": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.11.tgz"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "npm-asset/picocolors", "version": "1.1.1", "dist": {"type": "tar", "url": "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz"}, "type": "npm-asset", "license": ["ISC"]}, {"name": "npm-asset/postcss", "version": "8.5.6", "dist": {"type": "tar", "url": "https://registry.npmjs.org/postcss/-/postcss-8.5.6.tgz"}, "require": {"npm-asset/nanoid": ">=3.3.11,<4.0.0", "npm-asset/picocolors": ">=1.1.1,<2.0.0", "npm-asset/source-map-js": ">=1.2.1,<2.0.0"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "npm-asset/showdown", "version": "2.1.0", "dist": {"type": "tar", "url": "https://registry.npmjs.org/showdown/-/showdown-2.1.0.tgz"}, "require": {"npm-asset/commander": ">=9.0.0,<10.0.0"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "npm-asset/sortablejs", "version": "1.14.0", "dist": {"type": "tar", "url": "https://registry.npmjs.org/sortablejs/-/sortablejs-1.14.0.tgz"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "npm-asset/source-map-js", "version": "1.2.1", "dist": {"type": "tar", "url": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz"}, "type": "npm-asset", "license": ["BSD-3-<PERSON><PERSON>"]}, {"name": "npm-asset/vue", "version": "3.5.16", "dist": {"type": "tar", "url": "https://registry.npmjs.org/vue/-/vue-3.5.16.tgz"}, "require": {"npm-asset/vue--compiler-dom": "3.5.16", "npm-asset/vue--compiler-sfc": "3.5.16", "npm-asset/vue--runtime-dom": "3.5.16", "npm-asset/vue--server-renderer": "3.5.16", "npm-asset/vue--shared": "3.5.16"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "npm-asset/vue--compiler-core", "version": "3.5.16", "dist": {"type": "tar", "url": "https://registry.npmjs.org/@vue/compiler-core/-/compiler-core-3.5.16.tgz"}, "require": {"npm-asset/babel--parser": ">=7.27.2,<8.0.0", "npm-asset/entities": ">=4.5.0,<5.0.0", "npm-asset/estree-walker": ">=2.0.2,<3.0.0", "npm-asset/source-map-js": ">=1.2.1,<2.0.0", "npm-asset/vue--shared": "3.5.16"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "npm-asset/vue--compiler-dom", "version": "3.5.16", "dist": {"type": "tar", "url": "https://registry.npmjs.org/@vue/compiler-dom/-/compiler-dom-3.5.16.tgz"}, "require": {"npm-asset/vue--compiler-core": "3.5.16", "npm-asset/vue--shared": "3.5.16"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "npm-asset/vue--compiler-sfc", "version": "3.5.16", "dist": {"type": "tar", "url": "https://registry.npmjs.org/@vue/compiler-sfc/-/compiler-sfc-3.5.16.tgz"}, "require": {"npm-asset/babel--parser": ">=7.27.2,<8.0.0", "npm-asset/estree-walker": ">=2.0.2,<3.0.0", "npm-asset/magic-string": ">=0.30.17,<0.31.0", "npm-asset/postcss": ">=8.5.3,<9.0.0", "npm-asset/source-map-js": ">=1.2.1,<2.0.0", "npm-asset/vue--compiler-core": "3.5.16", "npm-asset/vue--compiler-dom": "3.5.16", "npm-asset/vue--compiler-ssr": "3.5.16", "npm-asset/vue--shared": "3.5.16"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "npm-asset/vue--compiler-ssr", "version": "3.5.16", "dist": {"type": "tar", "url": "https://registry.npmjs.org/@vue/compiler-ssr/-/compiler-ssr-3.5.16.tgz"}, "require": {"npm-asset/vue--compiler-dom": "3.5.16", "npm-asset/vue--shared": "3.5.16"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "npm-asset/vue--reactivity", "version": "3.5.16", "dist": {"type": "tar", "url": "https://registry.npmjs.org/@vue/reactivity/-/reactivity-3.5.16.tgz"}, "require": {"npm-asset/vue--shared": "3.5.16"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "npm-asset/vue--runtime-core", "version": "3.5.16", "dist": {"type": "tar", "url": "https://registry.npmjs.org/@vue/runtime-core/-/runtime-core-3.5.16.tgz"}, "require": {"npm-asset/vue--reactivity": "3.5.16", "npm-asset/vue--shared": "3.5.16"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "npm-asset/vue--runtime-dom", "version": "3.5.16", "dist": {"type": "tar", "url": "https://registry.npmjs.org/@vue/runtime-dom/-/runtime-dom-3.5.16.tgz"}, "require": {"npm-asset/csstype": ">=3.1.3,<4.0.0", "npm-asset/vue--reactivity": "3.5.16", "npm-asset/vue--runtime-core": "3.5.16", "npm-asset/vue--shared": "3.5.16"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "npm-asset/vue--server-renderer", "version": "3.5.16", "dist": {"type": "tar", "url": "https://registry.npmjs.org/@vue/server-renderer/-/server-renderer-3.5.16.tgz"}, "require": {"npm-asset/vue--compiler-ssr": "3.5.16", "npm-asset/vue--shared": "3.5.16"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "npm-asset/vue--shared", "version": "3.5.16", "dist": {"type": "tar", "url": "https://registry.npmjs.org/@vue/shared/-/shared-3.5.16.tgz"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "paragonie/constant_time_encoding", "version": "v2.6.3", "source": {"type": "git", "url": "https://github.com/paragonie/constant_time_encoding.git", "reference": "58c3f47f650c94ec05a151692652a868995d2938"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/paragonie/constant_time_encoding/v2.6.3/paragonie-constant_time_encoding-v2.6.3.zip", "reference": "58c3f47f650c94ec05a151692652a868995d2938", "shasum": ""}, "require": {"php": "^7|^8"}, "require-dev": {"phpunit/phpunit": "^6|^7|^8|^9", "vimeo/psalm": "^1|^2|^3|^4"}, "type": "library", "autoload": {"psr-4": {"ParagonIE\\ConstantTime\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com", "role": "Maintainer"}, {"name": "<PERSON> 'Sc00bz' <PERSON>", "email": "<EMAIL>", "homepage": "https://www.tobtu.com", "role": "Original Developer"}], "description": "Constant-time Implementations of RFC 4648 Encoding (Base-64, Base-32, Base-16)", "keywords": ["base16", "base32", "base32_decode", "base32_encode", "base64", "base64_decode", "base64_encode", "bin2hex", "encoding", "hex", "hex2bin", "rfc4648"], "time": "2022-06-14T06:56:20+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "time": "2020-10-15T08:29:30+00:00"}, {"name": "php-http/client-common", "version": "2.7.2", "source": {"type": "git", "url": "https://github.com/php-http/client-common.git", "reference": "0cfe9858ab9d3b213041b947c881d5b19ceeca46"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/client-common/zipball/0cfe9858ab9d3b213041b947c881d5b19ceeca46", "reference": "0cfe9858ab9d3b213041b947c881d5b19ceeca46", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/httplug": "^2.0", "php-http/message": "^1.6", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0 || ^2.0", "symfony/options-resolver": "~4.0.15 || ~4.1.9 || ^4.2.1 || ^5.0 || ^6.0 || ^7.0", "symfony/polyfill-php80": "^1.17"}, "require-dev": {"doctrine/instantiator": "^1.1", "guzzlehttp/psr7": "^1.4", "nyholm/psr7": "^1.2", "phpspec/phpspec": "^5.1 || ^6.3 || ^7.1", "phpspec/prophecy": "^1.10.2", "phpunit/phpunit": "^7.5.20 || ^8.5.33 || ^9.6.7"}, "suggest": {"ext-json": "To detect JSON responses with the ContentTypePlugin", "ext-libxml": "To detect XML responses with the ContentTypePlugin", "php-http/cache-plugin": "PSR-6 Cache plugin", "php-http/logger-plugin": "PSR-3 Logger plugin", "php-http/stopwatch-plugin": "Symfony Stopwatch plugin"}, "type": "library", "autoload": {"psr-4": {"Http\\Client\\Common\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common HTTP Client implementations and tools for HTTPlug", "homepage": "http://httplug.io", "keywords": ["client", "common", "http", "httplug"], "support": {"issues": "https://github.com/php-http/client-common/issues", "source": "https://github.com/php-http/client-common/tree/2.7.2"}, "time": "2024-09-24T06:21:48+00:00"}, {"name": "php-http/discovery", "version": "1.20.0", "source": {"type": "git", "url": "https://github.com/php-http/discovery.git", "reference": "82fe4c73ef3363caed49ff8dd1539ba06044910d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/discovery/zipball/82fe4c73ef3363caed49ff8dd1539ba06044910d", "reference": "82fe4c73ef3363caed49ff8dd1539ba06044910d", "shasum": ""}, "require": {"composer-plugin-api": "^1.0|^2.0", "php": "^7.1 || ^8.0"}, "conflict": {"nyholm/psr7": "<1.0", "zendframework/zend-diactoros": "*"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "*", "psr/http-factory-implementation": "*", "psr/http-message-implementation": "*"}, "require-dev": {"composer/composer": "^1.0.2|^2.0", "graham-campbell/phpspec-skip-example-extension": "^5.0", "php-http/httplug": "^1.0 || ^2.0", "php-http/message-factory": "^1.0", "phpspec/phpspec": "^5.1 || ^6.1 || ^7.3", "sebastian/comparator": "^3.0.5 || ^4.0.8", "symfony/phpunit-bridge": "^6.4.4 || ^7.0.1"}, "type": "composer-plugin", "extra": {"class": "Http\\Discovery\\Composer\\Plugin", "plugin-optional": true}, "autoload": {"psr-4": {"Http\\Discovery\\": "src/"}, "exclude-from-classmap": ["src/Composer/Plugin.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Finds and installs PSR-7, PSR-17, PSR-18 and HTTPlug implementations", "homepage": "http://php-http.org", "keywords": ["adapter", "client", "discovery", "factory", "http", "message", "psr17", "psr7"], "support": {"issues": "https://github.com/php-http/discovery/issues", "source": "https://github.com/php-http/discovery/tree/1.20.0"}, "time": "2024-10-02T11:20:13+00:00"}, {"name": "php-http/httplug", "version": "2.4.1", "source": {"type": "git", "url": "https://github.com/php-http/httplug.git", "reference": "5cad731844891a4c282f3f3e1b582c46839d22f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/httplug/zipball/5cad731844891a4c282f3f3e1b582c46839d22f4", "reference": "5cad731844891a4c282f3f3e1b582c46839d22f4", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/promise": "^1.1", "psr/http-client": "^1.0", "psr/http-message": "^1.0 || ^2.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.1 || ^5.0 || ^6.0", "phpspec/phpspec": "^5.1 || ^6.0 || ^7.0"}, "type": "library", "autoload": {"psr-4": {"Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Eric <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "HTTPlug, the HTTP client abstraction for PHP", "homepage": "http://httplug.io", "keywords": ["client", "http"], "support": {"issues": "https://github.com/php-http/httplug/issues", "source": "https://github.com/php-http/httplug/tree/2.4.1"}, "time": "2024-09-23T11:39:58+00:00"}, {"name": "php-http/message", "version": "1.16.2", "source": {"type": "git", "url": "https://github.com/php-http/message.git", "reference": "06dd5e8562f84e641bf929bfe699ee0f5ce8080a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message/zipball/06dd5e8562f84e641bf929bfe699ee0f5ce8080a", "reference": "06dd5e8562f84e641bf929bfe699ee0f5ce8080a", "shasum": ""}, "require": {"clue/stream-filter": "^1.5", "php": "^7.2 || ^8.0", "psr/http-message": "^1.1 || ^2.0"}, "provide": {"php-http/message-factory-implementation": "1.0"}, "require-dev": {"ergebnis/composer-normalize": "^2.6", "ext-zlib": "*", "guzzlehttp/psr7": "^1.0 || ^2.0", "laminas/laminas-diactoros": "^2.0 || ^3.0", "php-http/message-factory": "^1.0.2", "phpspec/phpspec": "^5.1 || ^6.3 || ^7.1", "slim/slim": "^3.0"}, "suggest": {"ext-zlib": "Used with compressor/decompressor streams", "guzzlehttp/psr7": "Used with Guzzle PSR-7 Factories", "laminas/laminas-diactoros": "Used with Diactoros Factories", "slim/slim": "Used with Slim Framework PSR-7 implementation"}, "type": "library", "autoload": {"files": ["src/filters.php"], "psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "HTTP Message related tools", "homepage": "http://php-http.org", "keywords": ["http", "message", "psr-7"], "support": {"issues": "https://github.com/php-http/message/issues", "source": "https://github.com/php-http/message/tree/1.16.2"}, "time": "2024-10-02T11:34:13+00:00"}, {"name": "php-http/message-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-http/message-factory.git", "reference": "4d8778e1c7d405cbb471574821c1ff5b68cc8f57"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message-factory/zipball/4d8778e1c7d405cbb471574821c1ff5b68cc8f57", "reference": "4d8778e1c7d405cbb471574821c1ff5b68cc8f57", "shasum": ""}, "require": {"php": ">=5.4", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Factory interfaces for PSR-7 HTTP Message", "homepage": "http://php-http.org", "keywords": ["factory", "http", "message", "stream", "uri"], "support": {"issues": "https://github.com/php-http/message-factory/issues", "source": "https://github.com/php-http/message-factory/tree/1.1.0"}, "abandoned": "psr/http-factory", "time": "2023-04-14T14:16:17+00:00"}, {"name": "php-http/promise", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/php-http/promise.git", "reference": "fc85b1fba37c169a69a07ef0d5a8075770cc1f83"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/promise/zipball/fc85b1fba37c169a69a07ef0d5a8075770cc1f83", "reference": "fc85b1fba37c169a69a07ef0d5a8075770cc1f83", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.3.2 || ^6.3", "phpspec/phpspec": "^5.1.2 || ^6.2 || ^7.4"}, "type": "library", "autoload": {"psr-4": {"Http\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Promise used for asynchronous HTTP requests", "homepage": "http://httplug.io", "keywords": ["promise"], "support": {"issues": "https://github.com/php-http/promise/issues", "source": "https://github.com/php-http/promise/tree/1.3.1"}, "time": "2024-03-15T13:55:21+00:00"}, {"name": "phpoffice/phpspreadsheet", "version": "1.29.11", "source": {"type": "git", "url": "https://github.com/PHPOffice/PhpSpreadsheet.git", "reference": "05b6c4378ddf3e81b460ea645c42b46432c0db25"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PhpSpreadsheet/zipball/05b6c4378ddf3e81b460ea645c42b46432c0db25", "reference": "05b6c4378ddf3e81b460ea645c42b46432c0db25", "shasum": ""}, "require": {"composer/pcre": "^1||^2||^3", "ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "ezyang/htmlpurifier": "^4.15", "maennchen/zipstream-php": "^2.1 || ^3.0", "markbaker/complex": "^3.0", "markbaker/matrix": "^3.0", "php": "^7.4 || ^8.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/simple-cache": "^1.0 || ^2.0 || ^3.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-main", "dompdf/dompdf": "^1.0 || ^2.0 || ^3.0", "friendsofphp/php-cs-fixer": "^3.2", "mitoteam/jpgraph": "^10.3", "mpdf/mpdf": "^8.1.1", "phpcompatibility/php-compatibility": "^9.3", "phpstan/phpstan": "^1.1", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^8.5 || ^9.0", "squizlabs/php_codesniffer": "^3.7", "tecnickcom/tcpdf": "^6.5"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer", "ext-intl": "PHP Internationalization Functions", "mitoteam/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "support": {"issues": "https://github.com/PHPOffice/PhpSpreadsheet/issues", "source": "https://github.com/PHPOffice/PhpSpreadsheet/tree/1.29.11"}, "time": "2025-06-23T01:22:06+00:00"}, {"name": "phpseclib/phpseclib", "version": "3.0.37", "source": {"type": "git", "url": "https://github.com/phpseclib/phpseclib.git", "reference": "cfa2013d0f68c062055180dd4328cc8b9d1f30b8"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/phpseclib/phpseclib/3.0.37/phpseclib-phpseclib-3.0.37.zip", "reference": "cfa2013d0f68c062055180dd4328cc8b9d1f30b8", "shasum": ""}, "require": {"paragonie/constant_time_encoding": "^1|^2", "paragonie/random_compat": "^1.4|^2.0|^9.99.99", "php": ">=5.6.1"}, "require-dev": {"phpunit/phpunit": "*"}, "suggest": {"ext-dom": "Install the DOM extension to load XML formatted public keys.", "ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "ext-libsodium": "SSH2/SFTP can make use of some algorithms provided by the libsodium-php extension.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.", "ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations."}, "type": "library", "autoload": {"files": ["phpseclib/bootstrap.php"], "psr-4": {"phpseclib3\\": "phpseclib/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "PHP Secure Communications Library - Pure-PHP implementations of RSA, AES, SSH2, SFTP, X.509 etc.", "homepage": "http://phpseclib.sourceforge.net", "keywords": ["BigInteger", "aes", "asn.1", "asn1", "blowfish", "crypto", "cryptography", "encryption", "rsa", "security", "sftp", "signature", "signing", "ssh", "twofish", "x.509", "x509"], "funding": [{"url": "https://github.com/terrafrost", "type": "github"}, {"url": "https://www.patreon.com/phpseclib", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/phpseclib/phpseclib", "type": "tidelift"}], "time": "2024-03-03T02:14:58+00:00"}, {"name": "predis/predis", "version": "v2.4.0", "source": {"type": "git", "url": "https://github.com/predis/predis.git", "reference": "f49e13ee3a2a825631562aa0223ac922ec5d058b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/predis/predis/zipball/f49e13ee3a2a825631562aa0223ac922ec5d058b", "reference": "f49e13ee3a2a825631562aa0223ac922ec5d058b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.3", "phpstan/phpstan": "^1.9", "phpunit/phpcov": "^6.0 || ^8.0", "phpunit/phpunit": "^8.0 || ^9.4"}, "suggest": {"ext-relay": "Faster connection with in-memory caching (>=0.6.2)"}, "type": "library", "autoload": {"psr-4": {"Predis\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "https://till.im", "role": "Maintainer"}], "description": "A flexible and feature-complete Redis/Valkey client for PHP.", "homepage": "http://github.com/predis/predis", "keywords": ["nosql", "predis", "redis"], "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v2.4.0"}, "funding": [{"url": "https://github.com/sponsors/tillkruss", "type": "github"}], "time": "2025-04-30T15:16:02+00:00"}, {"name": "psr/cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/master"}, "time": "2016-08-06T20:24:11+00:00"}, {"name": "psr/clock", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/clock.git", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/clock/zipball/e41a24703d4560fd0acb709162f73b8adfc3aa0d", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d", "shasum": ""}, "require": {"php": "^7.0 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Clock\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for reading the clock.", "homepage": "https://github.com/php-fig/clock", "keywords": ["clock", "now", "psr", "psr-20", "time"], "support": {"issues": "https://github.com/php-fig/clock/issues", "source": "https://github.com/php-fig/clock/tree/1.0.0"}, "time": "2022-11-25T14:36:26+00:00"}, {"name": "psr/container", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/513e0666f7216c7459170d56df27dfcefe1689ea", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/1.1.2"}, "time": "2021-11-05T16:50:12+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "1.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/1.1"}, "time": "2023-04-04T09:50:52+00:00"}, {"name": "psr/log", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.4"}, "time": "2021-05-03T11:20:27+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/master"}, "time": "2017-10-23T01:57:42+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "select2/select2", "version": "4.0.13", "source": {"type": "git", "url": "https://github.com/select2/select2.git", "reference": "45f2b83ceed5231afa7b3d5b12b58ad335edd82e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/select2/select2/zipball/45f2b83ceed5231afa7b3d5b12b58ad335edd82e", "reference": "45f2b83ceed5231afa7b3d5b12b58ad335edd82e", "shasum": ""}, "type": "component", "extra": {"component": {"files": ["dist/js/select2.js", "dist/js/i18n/*.js", "dist/css/select2.css"], "styles": ["dist/css/select2.css"], "scripts": ["dist/js/select2.js"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Select2 is a jQuery based replacement for select boxes.", "homepage": "https://select2.org/", "support": {"issues": "https://github.com/select2/select2/issues", "source": "https://github.com/select2/select2/tree/4.0.13"}, "time": "2020-01-28T05:01:22+00:00"}, {"name": "sentry/sdk", "version": "3.6.0", "source": {"type": "git", "url": "https://github.com/getsentry/sentry-php-sdk.git", "reference": "24c235ff2027401cbea099bf88689e1a1f197c7a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/getsentry/sentry-php-sdk/zipball/24c235ff2027401cbea099bf88689e1a1f197c7a", "reference": "24c235ff2027401cbea099bf88689e1a1f197c7a", "shasum": ""}, "require": {"http-interop/http-factory-guzzle": "^1.0", "sentry/sentry": "^3.22", "symfony/http-client": "^4.3|^5.0|^6.0|^7.0"}, "type": "metapackage", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Sentry", "email": "<EMAIL>"}], "description": "This is a metapackage shipping sentry/sentry with a recommended HTTP client.", "homepage": "http://sentry.io", "keywords": ["crash-reporting", "crash-reports", "error-handler", "error-monitoring", "log", "logging", "sentry"], "support": {"issues": "https://github.com/getsentry/sentry-php-sdk/issues", "source": "https://github.com/getsentry/sentry-php-sdk/tree/3.6.0"}, "funding": [{"url": "https://sentry.io/", "type": "custom"}, {"url": "https://sentry.io/pricing/", "type": "custom"}], "time": "2023-12-04T10:49:33+00:00"}, {"name": "sentry/sentry", "version": "3.22.1", "source": {"type": "git", "url": "https://github.com/getsentry/sentry-php.git", "reference": "8859631ba5ab15bc1af420b0eeed19ecc6c9d81d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/getsentry/sentry-php/zipball/8859631ba5ab15bc1af420b0eeed19ecc6c9d81d", "reference": "8859631ba5ab15bc1af420b0eeed19ecc6c9d81d", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "guzzlehttp/promises": "^1.5.3|^2.0", "jean85/pretty-package-versions": "^1.5|^2.0.4", "php": "^7.2|^8.0", "php-http/async-client-implementation": "^1.0", "php-http/client-common": "^1.5|^2.0", "php-http/discovery": "^1.15", "php-http/httplug": "^1.1|^2.0", "php-http/message": "^1.5", "php-http/message-factory": "^1.1", "psr/http-factory": "^1.0", "psr/http-factory-implementation": "^1.0", "psr/log": "^1.0|^2.0|^3.0", "symfony/options-resolver": "^3.4.43|^4.4.30|^5.0.11|^6.0|^7.0", "symfony/polyfill-php80": "^1.17"}, "conflict": {"php-http/client-common": "1.8.0", "raven/raven": "*"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.19|3.4.*", "guzzlehttp/psr7": "^1.8.4|^2.1.1", "http-interop/http-factory-guzzle": "^1.0", "monolog/monolog": "^1.6|^2.0|^3.0", "nikic/php-parser": "^4.10.3", "php-http/mock-client": "^1.3", "phpbench/phpbench": "^1.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.3", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^8.5.14|^9.4", "symfony/phpunit-bridge": "^5.2|^6.0", "vimeo/psalm": "^4.17"}, "suggest": {"monolog/monolog": "Allow sending log messages to Sentry by using the included Monolog handler."}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"Sentry\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Sentry", "email": "<EMAIL>"}], "description": "A PHP SDK for Sentry (http://sentry.io)", "homepage": "http://sentry.io", "keywords": ["crash-reporting", "crash-reports", "error-handler", "error-monitoring", "log", "logging", "sentry"], "support": {"issues": "https://github.com/getsentry/sentry-php/issues", "source": "https://github.com/getsentry/sentry-php/tree/3.22.1"}, "funding": [{"url": "https://sentry.io/", "type": "custom"}, {"url": "https://sentry.io/pricing/", "type": "custom"}], "time": "2023-11-13T11:47:28+00:00"}, {"name": "symfony/cache", "version": "v5.4.46", "source": {"type": "git", "url": "https://github.com/symfony/cache.git", "reference": "0fe08ee32cec2748fbfea10c52d3ee02049e0f6b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache/zipball/0fe08ee32cec2748fbfea10c52d3ee02049e0f6b", "reference": "0fe08ee32cec2748fbfea10c52d3ee02049e0f6b", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/cache": "^1.0|^2.0", "psr/log": "^1.1|^2|^3", "symfony/cache-contracts": "^1.1.7|^2", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2|^3", "symfony/var-exporter": "^4.4|^5.0|^6.0"}, "conflict": {"doctrine/dbal": "<2.13.1", "symfony/dependency-injection": "<4.4", "symfony/http-kernel": "<4.4", "symfony/var-dumper": "<4.4"}, "provide": {"psr/cache-implementation": "1.0|2.0", "psr/simple-cache-implementation": "1.0|2.0", "symfony/cache-implementation": "1.0|2.0"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/cache": "^1.6|^2.0", "doctrine/dbal": "^2.13.1|^3|^4", "predis/predis": "^1.1|^2.0", "psr/simple-cache": "^1.0|^2.0", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/filesystem": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^4.4|^5.0|^6.0", "symfony/messenger": "^4.4|^5.0|^6.0", "symfony/var-dumper": "^4.4|^5.0|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Cache\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides extended PSR-6, PSR-16 (and tags) implementations", "homepage": "https://symfony.com", "keywords": ["caching", "psr6"], "support": {"source": "https://github.com/symfony/cache/tree/v5.4.46"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-04T11:43:55+00:00"}, {"name": "symfony/cache-contracts", "version": "v2.5.4", "source": {"type": "git", "url": "https://github.com/symfony/cache-contracts.git", "reference": "517c3a3619dadfa6952c4651767fcadffb4df65e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache-contracts/zipball/517c3a3619dadfa6952c4651767fcadffb4df65e", "reference": "517c3a3619dadfa6952c4651767fcadffb4df65e", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/cache": "^1.0|^2.0|^3.0"}, "suggest": {"symfony/cache-implementation": ""}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "2.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Cache\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to caching", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/cache-contracts/tree/v2.5.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v2.5.4", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "605389f2a7e5625f273b53960dc46aeaf9c62918"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/605389f2a7e5625f273b53960dc46aeaf9c62918", "reference": "605389f2a7e5625f273b53960dc46aeaf9c62918", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "2.5-dev"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.5.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/event-dispatcher", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "72982eb416f61003e9bb6e91f8b3213600dcf9e9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/72982eb416f61003e9bb6e91f8b3213600dcf9e9", "reference": "72982eb416f61003e9bb6e91f8b3213600dcf9e9", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/event-dispatcher-contracts": "^2|^3", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/dependency-injection": "<4.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/http-foundation": "^4.4|^5.0|^6.0", "symfony/service-contracts": "^1.1|^2|^3", "symfony/stopwatch": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v2.5.4", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "e0fe3d79b516eb75126ac6fa4cbf19b79b08c99f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/e0fe3d79b516eb75126ac6fa4cbf19b79b08c99f", "reference": "e0fe3d79b516eb75126ac6fa4cbf19b79b08c99f", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/event-dispatcher": "^1"}, "suggest": {"symfony/event-dispatcher-implementation": ""}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "2.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v2.5.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/expression-language", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/expression-language.git", "reference": "a784b66edc4c151eb05076d04707906ee2c209a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/expression-language/zipball/a784b66edc4c151eb05076d04707906ee2c209a9", "reference": "a784b66edc4c151eb05076d04707906ee2c209a9", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/cache": "^4.4|^5.0|^6.0", "symfony/service-contracts": "^1.1|^2|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ExpressionLanguage\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an engine that can compile and evaluate expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/expression-language/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-04T14:55:40+00:00"}, {"name": "symfony/http-client", "version": "v5.4.49", "source": {"type": "git", "url": "https://github.com/symfony/http-client.git", "reference": "d77d8e212cde7b5c4a64142bf431522f19487c28"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client/zipball/d77d8e212cde7b5c4a64142bf431522f19487c28", "reference": "d77d8e212cde7b5c4a64142bf431522f19487c28", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.1|^3", "symfony/http-client-contracts": "^2.5.4", "symfony/polyfill-php73": "^1.11", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.0|^2|^3"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "1.0", "symfony/http-client-implementation": "2.4"}, "require-dev": {"amphp/amp": "^2.5", "amphp/http-client": "^4.2.1", "amphp/http-tunnel": "^1.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.4|^2.0", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "php-http/message-factory": "^1.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^4.4.13|^5.1.5|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/stopwatch": "^4.4|^5.0|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpClient\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides powerful methods to fetch HTTP resources synchronously or asynchronously", "homepage": "https://symfony.com", "keywords": ["http"], "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.49"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-28T08:37:04+00:00"}, {"name": "symfony/http-client-contracts", "version": "v2.5.5", "source": {"type": "git", "url": "https://github.com/symfony/http-client-contracts.git", "reference": "48ef1d0a082885877b664332b9427662065a360c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/48ef1d0a082885877b664332b9427662065a360c", "reference": "48ef1d0a082885877b664332b9427662065a360c", "shasum": ""}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/http-client-implementation": ""}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "2.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\HttpClient\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to HTTP clients", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v2.5.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-28T08:37:04+00:00"}, {"name": "symfony/mailer", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/mailer.git", "reference": "f732e1fafdf0f4a2d865e91f1018aaca174aeed9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mailer/zipball/f732e1fafdf0f4a2d865e91f1018aaca174aeed9", "reference": "f732e1fafdf0f4a2d865e91f1018aaca174aeed9", "shasum": ""}, "require": {"egulias/email-validator": "^2.1.10|^3|^4", "php": ">=7.2.5", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.1|^3", "symfony/event-dispatcher": "^4.4|^5.0|^6.0", "symfony/mime": "^5.2.6|^6.0", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2|^3"}, "conflict": {"symfony/http-kernel": "<4.4"}, "require-dev": {"symfony/http-client": "^4.4|^5.0|^6.0", "symfony/messenger": "^4.4|^5.0|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mailer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps sending emails", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/mailer/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/mime", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "8c1b9b3e5b52981551fc6044539af1d974e39064"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/8c1b9b3e5b52981551fc6044539af1d974e39064", "reference": "8c1b9b3e5b52981551fc6044539af1d974e39064", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<4.4", "symfony/serializer": "<5.4.35|>=6,<6.3.12|>=6.4,<6.4.3"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/process": "^5.4|^6.4", "symfony/property-access": "^4.4|^5.1|^6.0", "symfony/property-info": "^4.4|^5.1|^6.0", "symfony/serializer": "^5.4.35|~6.3.12|^6.4.3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-23T20:18:32+00:00"}, {"name": "symfony/options-resolver", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "74e5b6f0db3e8589e6cfd5efb317a1fc2bb52fb6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/74e5b6f0db3e8589e6cfd5efb317a1fc2bb52fb6", "reference": "74e5b6f0db3e8589e6cfd5efb317a1fc2bb52fb6", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php73": "~1.0", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an improved replacement for the array_replace PHP function", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "support": {"source": "https://github.com/symfony/options-resolver/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/polyfill", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill.git", "reference": "c4ee386e95ccdbea59cea802ea776d806319d506"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill/zipball/c4ee386e95ccdbea59cea802ea776d806319d506", "reference": "c4ee386e95ccdbea59cea802ea776d806319d506", "shasum": ""}, "require": {"php": ">=7.2"}, "replace": {"symfony/polyfill-apcu": "self.version", "symfony/polyfill-ctype": "self.version", "symfony/polyfill-iconv": "self.version", "symfony/polyfill-intl-grapheme": "self.version", "symfony/polyfill-intl-icu": "self.version", "symfony/polyfill-intl-idn": "self.version", "symfony/polyfill-intl-messageformatter": "self.version", "symfony/polyfill-intl-normalizer": "self.version", "symfony/polyfill-mbstring": "self.version", "symfony/polyfill-php73": "self.version", "symfony/polyfill-php74": "self.version", "symfony/polyfill-php80": "self.version", "symfony/polyfill-php81": "self.version", "symfony/polyfill-php82": "self.version", "symfony/polyfill-php83": "self.version", "symfony/polyfill-php84": "self.version", "symfony/polyfill-php85": "self.version", "symfony/polyfill-util": "self.version", "symfony/polyfill-uuid": "self.version"}, "require-dev": {"symfony/intl": "^5.4|^6.4", "symfony/phpunit-bridge": "^6.4", "symfony/var-dumper": "^5.4|^6.4"}, "type": "library", "autoload": {"files": ["src/bootstrap.php", "src/Apcu/bootstrap.php", "src/Ctype/bootstrap.php", "src/Uuid/bootstrap.php", "src/Iconv/bootstrap.php", "src/Intl/Grapheme/bootstrap.php", "src/Intl/Idn/bootstrap.php", "src/Intl/Icu/bootstrap.php", "src/Intl/MessageFormatter/bootstrap.php", "src/Intl/Normalizer/bootstrap.php", "src/Mbstring/bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\": "src/"}, "classmap": ["src/Intl/Icu/Resources/stubs", "src/Intl/MessageFormatter/Resources/stubs", "src/Intl/Normalizer/Resources/stubs", "src/Php85/Resources/stubs", "src/Php84/Resources/stubs", "src/Php83/Resources/stubs", "src/Php82/Resources/stubs", "src/Php81/Resources/stubs", "src/Php80/Resources/stubs", "src/Php73/Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfills backporting features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compat", "compatibility", "dev", "polyfill", "shim"], "support": {"issues": "https://github.com/symfony/polyfill/issues", "source": "https://github.com/symfony/polyfill/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-02T09:40:28+00:00"}, {"name": "symfony/process", "version": "v5.4.47", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "5d1662fb32ebc94f17ddb8d635454a776066733d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/5d1662fb32ebc94f17ddb8d635454a776066733d", "reference": "5d1662fb32ebc94f17ddb8d635454a776066733d", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v5.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-06T11:36:42+00:00"}, {"name": "symfony/service-contracts", "version": "v2.5.4", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "f37b419f7aea2e9abf10abd261832cace12e3300"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/f37b419f7aea2e9abf10abd261832cace12e3300", "reference": "f37b419f7aea2e9abf10abd261832cace12e3300", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/container": "^1.1", "symfony/deprecation-contracts": "^2.1|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "suggest": {"symfony/service-implementation": ""}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "2.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v2.5.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/translation", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "98f26acc99341ca4bab345fb14d7b1d7cb825bed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/98f26acc99341ca4bab345fb14d7b1d7cb825bed", "reference": "98f26acc99341ca4bab345fb14d7b1d7cb825bed", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation-contracts": "^2.3"}, "conflict": {"symfony/config": "<4.4", "symfony/console": "<5.3", "symfony/dependency-injection": "<5.0", "symfony/http-kernel": "<5.0", "symfony/twig-bundle": "<5.0", "symfony/yaml": "<4.4"}, "provide": {"symfony/translation-implementation": "2.3"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^4.4|^5.0|^6.0", "symfony/console": "^5.4|^6.0", "symfony/dependency-injection": "^5.0|^6.0", "symfony/finder": "^4.4|^5.0|^6.0", "symfony/http-client-contracts": "^1.1|^2.0|^3.0", "symfony/http-kernel": "^5.0|^6.0", "symfony/intl": "^4.4|^5.0|^6.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/service-contracts": "^1.1.2|^2|^3", "symfony/yaml": "^4.4|^5.0|^6.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/translation-contracts", "version": "v2.5.4", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "450d4172653f38818657022252f9d81be89ee9a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/450d4172653f38818657022252f9d81be89ee9a8", "reference": "450d4172653f38818657022252f9d81be89ee9a8", "shasum": ""}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/translation-implementation": ""}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "2.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v2.5.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/var-exporter", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/var-exporter.git", "reference": "862700068db0ddfd8c5b850671e029a90246ec75"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-exporter/zipball/862700068db0ddfd8c5b850671e029a90246ec75", "reference": "862700068db0ddfd8c5b850671e029a90246ec75", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"symfony/var-dumper": "^4.4.9|^5.0.9|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\VarExporter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows exporting any serializable PHP data structure to plain PHP code", "homepage": "https://symfony.com", "keywords": ["clone", "construct", "export", "hydrate", "instantiate", "serialize"], "support": {"source": "https://github.com/symfony/var-exporter/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/yaml", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "a454d47278cc16a5db371fe73ae66a78a633371e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/a454d47278cc16a5db371fe73ae66a78a633371e", "reference": "a454d47278cc16a5db371fe73ae66a78a633371e", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/console": "<5.3"}, "require-dev": {"symfony/console": "^5.3|^6.0"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "bin": ["Resources/bin/yaml-lint"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "waterank/audit", "version": "3.14.0", "source": {"type": "git", "url": "************************:xlerr/audit.git", "reference": "daa12796c9be774c3ef75f5901d978b0cd1a552c"}, "dist": {"type": "zip", "url": "https://biz-satis-test.kuainiujinke.com/composer/dist/waterank/audit/waterank-audit-3.14.0-046925.zip", "reference": "daa12796c9be774c3ef75f5901d978b0cd1a552c"}, "require": {"ext-json": "*", "league/oauth2-client": "^2.6", "php": ">=7.4", "symfony/polyfill": "^1.26", "xlerr/yii2-common-widgets": "^1.11", "xlerr/yii2-widget-code-editor": "^1.1", "yiisoft/yii2": "^2.0"}, "require-dev": {"symfony/var-dumper": "*", "xlerr/kvmanager": "^4.3", "xlerr/task": "^2.0", "xlerr/yii2-adminlte": "^2.1", "yiisoft/yii2-bootstrap": "^2.0", "yiisoft/yii2-debug": "^2.1", "yiisoft/yii2-gii": "^2.2"}, "type": "yii2-extension", "autoload": {"psr-4": {"waterank\\audit\\": "src"}, "files": ["./src/functions.php"]}, "autoload-dev": {"psr-4": {"waterank\\tests\\": "tests"}}, "license": ["MIT"], "authors": [{"name": "chenquan", "email": "<EMAIL>"}], "description": "kuainiu group audit component", "homepage": "https://git.kuainiujinke.com/xlerr/audit.git", "time": "2025-07-29T07:27:51+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "time": "2022-06-03T18:03:27+00:00"}, {"name": "xlerr/application-payment", "version": "1.1.8", "source": {"type": "git", "url": "************************:xlerr/application-payment.git", "reference": "aadacc0782bd6392fd3047a526b91b6c0feea7da"}, "dist": {"type": "zip", "url": "https://biz-satis-test.kuainiujinke.com/composer/dist/xlerr/application-payment/xlerr-application-payment-1.1.8-301ebd.zip", "reference": "aadacc0782bd6392fd3047a526b91b6c0feea7da"}, "require": {"nesbot/carbon": "^2.44", "php": "^7.4 || ^8.0 || ^8.1", "xlerr/settlement-flow": "^3.0", "xlerr/task": "^2.0", "yiisoft/yii2": "^2.0"}, "require-dev": {"roave/security-advisories": "dev-latest", "symfony/var-dumper": "^5.4"}, "type": "library", "autoload": {"psr-4": {"Xlerr\\ApplicationPayment\\": "src/"}}, "autoload-dev": {"psr-4": {"Xlerr\\ApplicationPaymentTests\\": "tests/"}}, "license": ["MIT"], "authors": [{"name": "jamespan", "email": "<EMAIL>"}], "description": "财务系统付款申请", "time": "2025-07-09T08:12:49+00:00"}, {"name": "xlerr/cmdb", "version": "2.0.22", "source": {"type": "git", "url": "************************:xlerr/cmdb.git", "reference": "0c1b8d92753d66beb1ecd92b90ccedbcaecb3b7f"}, "dist": {"type": "zip", "url": "https://biz-satis-test.kuainiujinke.com/composer/dist/xlerr/cmdb/xlerr-cmdb-2.0.22-1e79cb.zip", "reference": "0c1b8d92753d66beb1ecd92b90ccedbcaecb3b7f"}, "require": {"kartik-v/yii2-detail-view": "^1.8", "kartik-v/yii2-editable": "^1.7", "kartik-v/yii2-field-range": "^1.3", "kartik-v/yii2-grid": "^3.3", "nesbot/carbon": "^2.44", "waterank/audit": "^3.4", "xlerr/desensitise": "^2.0", "xlerr/httpca": "^1.1", "xlerr/kvmanager": "^4.3", "xlerr/yii2-common-widgets": ">= 1.8.8", "xlerr/yii2-widget-code-editor": "^1.1", "yiisoft/yii2": "^2.0"}, "type": "yii2-extension", "autoload": {"psr-4": {"cmdb\\": "."}}, "description": "cmdb", "time": "2024-03-13T06:05:53+00:00"}, {"name": "xlerr/common-payment", "version": "1.5.0", "source": {"type": "git", "url": "************************:xlerr/common-payment.git", "reference": "d6d75ae190bafd61d576c1ba68b1ba61d881f0f3"}, "dist": {"type": "zip", "url": "https://biz-satis-test.kuainiujinke.com/composer/dist/xlerr/common-payment/xlerr-common-payment-1.5.0-afcf8b.zip", "reference": "d6d75ae190bafd61d576c1ba68b1ba61d881f0f3"}, "require": {"php": ">=7.4", "waterank/audit": "*", "xlerr/kvmanager": "*", "xlerr/yii2-common-widgets": "*", "yiisoft/yii2": "*"}, "require-dev": {"roave/security-advisories": "dev-latest"}, "type": "yii2-extension", "autoload": {"psr-4": {"CommonPayment\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "潘小龙", "email": "<EMAIL>"}], "description": "common payment", "time": "2024-01-03T03:02:08+00:00"}, {"name": "xlerr/cost-management", "version": "0.0.6", "source": {"type": "git", "url": "************************:xlerr/cost-management.git", "reference": "60061768f05ab66eb3e51cf27e63ce3c0ea181ee"}, "dist": {"type": "zip", "url": "https://biz-satis-test.kuainiujinke.com/composer/dist/xlerr/cost-management/xlerr-cost-management-0.0.6-aeebd8.zip", "reference": "60061768f05ab66eb3e51cf27e63ce3c0ea181ee"}, "require": {"ext-json": "*", "nesbot/carbon": "^2.0", "php": ">=7.4", "xlerr/yii2-common-widgets": "^1.0", "yiisoft/yii2": ">=2.0.13 <3.0"}, "type": "yii2-extension", "autoload": {"psr-4": {"Xlerr\\CostManagement\\": "src"}}, "license": ["MIT"], "authors": [{"name": "xlerr", "email": "<EMAIL>"}], "description": "yii2 task", "time": "2025-07-31T06:50:45+00:00"}, {"name": "xlerr/cpop-income", "version": "0.1.20", "source": {"type": "git", "url": "************************:xlerr/cpop-income.git", "reference": "3e2563dd31f8e361f1b131d1f54591aced1ee6c8"}, "dist": {"type": "zip", "url": "https://biz-satis-test.kuainiujinke.com/composer/dist/xlerr/cpop-income/xlerr-cpop-income-0.1.20-7bc267.zip", "reference": "3e2563dd31f8e361f1b131d1f54591aced1ee6c8"}, "require": {"ext-json": "*", "nesbot/carbon": "^2.0", "php": ">=7.4", "waterank/audit": ">= ********", "xlerr/settlement-flow": "^2.1 || ^3.0", "xlerr/yii2-common-widgets": "^1.5", "yiisoft/yii2": ">=2.0.13 <3.0"}, "require-dev": {"yiisoft/yii2-debug": "~2.0.0"}, "type": "yii2-extension", "autoload": {"psr-4": {"Xlerr\\CpopIncome\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "heping", "email": "<EMAIL>"}], "description": "description", "time": "2025-07-29T03:18:21+00:00"}, {"name": "xlerr/cpop-offline", "version": "0.0.7", "source": {"type": "git", "url": "************************:xlerr/cpop-offline.git", "reference": "15366ae4db0eca0a8208350728eab4f3ebfdff56"}, "dist": {"type": "zip", "url": "https://biz-satis-test.kuainiujinke.com/composer/dist/xlerr/cpop-offline/xlerr-cpop-offline-0.0.7-1d1323.zip", "reference": "15366ae4db0eca0a8208350728eab4f3ebfdff56"}, "require": {"nesbot/carbon": "^2.0", "php": ">=7.4", "xlerr/task": "*", "yiisoft/yii2": ">=2.0.13 <3.0"}, "type": "yii2-extension", "autoload": {"psr-4": {"Xlerr\\CpopOffline\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "jamespan", "email": "<EMAIL>"}], "description": "description", "time": "2025-07-09T09:46:30+00:00"}, {"name": "xlerr/datasource", "version": "4.5.26", "source": {"type": "git", "url": "************************:xlerr/datasource.git", "reference": "59ceff037a533140b2903c3072b7a18710131044"}, "dist": {"type": "zip", "url": "https://biz-satis-test.kuainiujinke.com/composer/dist/xlerr/datasource/xlerr-datasource-4.5.26-38fcdd.zip", "reference": "59ceff037a533140b2903c3072b7a18710131044"}, "require": {"dragonmantank/cron-expression": "^3.3", "ext-curl": "*", "ext-json": "*", "ext-zlib": "*", "kartik-v/yii2-tabs-x": "^1.2", "nesbot/carbon": "*", "php": "^7.4 || ^8.0", "predis/predis": "^2.1", "waterank/audit": "^3.0", "xlerr/desensitise": "^2.0", "xlerr/group": "^1.0", "xlerr/httpca": "^1.2", "xlerr/kvmanager": "^4.3", "xlerr/lock": "^1.0", "xlerr/task": "^2.0", "xlerr/yii2-adminlte": "^2.1 || ^3.0", "xlerr/yii2-common-widgets": ">=1.13.0 < 2.0", "xlerr/yii2-widget-code-editor": "^1.1", "yiisoft/yii2": "^2.0", "yiisoft/yii2-bootstrap": "^2.0", "yiisoft/yii2-symfonymailer": "*"}, "require-dev": {"roave/security-advisories": "dev-latest", "symfony/var-dumper": "^5.4", "yiisoft/yii2-debug": "^2.1", "yiisoft/yii2-gii": "^2.2"}, "type": "yii2-extension", "autoload": {"psr-4": {"datasource\\": "src"}}, "autoload-dev": {"psr-4": {"datasource\\tests\\": "tests"}}, "scripts": {"task": ["cd ./tests && ./yii dashboard-data-generate && ./yii task/process-all"]}, "license": ["MIT"], "authors": [{"name": "panxiaolong", "email": "<EMAIL>"}], "description": "kuainiu group datasource component", "homepage": "https://git.kuainiujinke.com/xlerr/datasource.git", "time": "2025-07-01T06:54:38+00:00"}, {"name": "xlerr/desensitise", "version": "2.0.10", "source": {"type": "git", "url": "************************:xlerr/desensitise.git", "reference": "bd853afad63f0f426a29a71023b87e32eebfdac2"}, "dist": {"type": "zip", "url": "https://biz-satis-test.kuainiujinke.com/composer/dist/xlerr/desensitise/xlerr-desensitise-2.0.10-b6fc23.zip", "reference": "bd853afad63f0f426a29a71023b87e32eebfdac2"}, "require": {"php": ">=7.1", "xlerr/httpca": ">=0.0.3 <1.0 || ^1.1", "yiisoft/yii2": "^2.0"}, "type": "library", "autoload": {"psr-4": {"xlerr\\desensitise\\": "src"}, "files": ["src/functions.php"]}, "license": ["MIT"], "authors": [{"name": "xlerr", "email": "<EMAIL>"}], "time": "2022-10-26T03:09:05+00:00"}, {"name": "xlerr/diy-report", "version": "0.0.15", "source": {"type": "git", "url": "************************:xlerr/diy-report.git", "reference": "26f786b74af366a8728c104d5100d057635c74bf"}, "dist": {"type": "zip", "url": "https://biz-satis-test.kuainiujinke.com/composer/dist/xlerr/diy-report/xlerr-diy-report-0.0.15-c5656f.zip", "reference": "26f786b74af366a8728c104d5100d057635c74bf"}, "require": {"kartik-v/yii2-grid": "^3.3", "nesbot/carbon": "^2.44", "symfony/expression-language": "^5.2", "xlerr/datasource": "^4.0", "xlerr/httpca": "^1.1", "xlerr/kvmanager": "^4.3", "xlerr/yii2-common-widgets": ">= 1.8.8", "xlerr/yii2-widget-code-editor": "^1.1", "yiisoft/yii2": "^2.0"}, "require-dev": {"roave/security-advisories": "dev-latest"}, "type": "yii2-extension", "autoload": {"psr-4": {"DiyReport\\": "src"}}, "authors": [{"name": "heping", "email": "<EMAIL>"}], "description": "diy-report", "time": "2025-07-25T02:41:45+00:00"}, {"name": "xlerr/fullcalendar", "version": "1.0.5", "source": {"type": "git", "url": "************************:xlerr/fullcalendar.git", "reference": "cd6369548cd13343b1a85c6b86441043b0e956b9"}, "dist": {"type": "zip", "url": "https://biz-satis-test.kuainiujinke.com/composer/dist/xlerr/fullcalendar/xlerr-fullcalendar-1.0.5-13b2df.zip", "reference": "cd6369548cd13343b1a85c6b86441043b0e956b9"}, "require": {"php": ">=7.4", "yiisoft/yii2": ">=2.0.14 <3.0"}, "type": "library", "autoload": {"psr-4": {"xlerr\\fullcalendar\\": "src"}}, "license": ["MIT"], "authors": [{"name": "xlerr", "email": "<EMAIL>"}], "description": "yii2 fullcalendar", "keywords": ["fullcalendar", "yii2"], "time": "2023-12-22T05:13:42+00:00"}, {"name": "xlerr/group", "version": "1.0.0", "source": {"type": "git", "url": "************************:xlerr/group.git", "reference": "ee7fc02c642bbf2079e78bdd6304267ade9cf3cf"}, "dist": {"type": "zip", "url": "https://biz-satis-test.kuainiujinke.com/composer/dist/xlerr/group/xlerr-group-1.0.0-b861c7.zip", "reference": "ee7fc02c642bbf2079e78bdd6304267ade9cf3cf"}, "require": {"php": "^7.4 || ^8.0", "xlerr/kvmanager": "^4.0", "yiisoft/yii2": "^2.0"}, "require-dev": {"roave/security-advisories": "dev-latest"}, "type": "library", "autoload": {"psr-4": {"xlerr\\group\\": "src/"}}, "license": ["MIT"], "description": "user group", "time": "2023-05-31T07:12:09+00:00"}, {"name": "xlerr/httpca", "version": "1.3.0", "source": {"type": "git", "url": "************************:xlerr/httpca.git", "reference": "058a01655e8942f2d4e88a86ba31551027a0ef6c"}, "dist": {"type": "zip", "url": "https://biz-satis-test.kuainiujinke.com/composer/dist/xlerr/httpca/xlerr-httpca-1.3.0-4ad698.zip", "reference": "058a01655e8942f2d4e88a86ba31551027a0ef6c"}, "require": {"guzzlehttp/guzzle": "^7.2", "php": "^7.0 || ^8.0", "yiisoft/yii2": ">=2.0.13"}, "type": "library", "autoload": {"psr-4": {"xlerr\\httpca\\": "src"}}, "license": ["MIT"], "authors": [{"name": "xlerr", "email": "<EMAIL>"}], "time": "2022-12-09T06:52:41+00:00"}, {"name": "xlerr/jasypt", "version": "1.0.4", "source": {"type": "git", "url": "************************:xlerr/jasypt.git", "reference": "7e096f9b08938609b074754b60b21f70c2fd68bf"}, "dist": {"type": "zip", "url": "https://biz-satis-test.kuainiujinke.com/composer/dist/xlerr/jasypt/xlerr-jasypt-1.0.4-9d2f3e.zip", "reference": "7e096f9b08938609b074754b60b21f70c2fd68bf"}, "require": {"symfony/process": "^5.4 || ^6.0"}, "bin": ["bin/loadRemoteConfig"], "type": "yii2-extension", "autoload": {"psr-4": {"xlerr\\jasypt\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "xlerr", "email": "<EMAIL>"}], "description": "jasy<PERSON>", "time": "2023-06-29T06:38:47+00:00"}, {"name": "xlerr/kvmanager", "version": "4.15.0", "source": {"type": "git", "url": "************************:xlerr/kvmanager.git", "reference": "5c5dc2fba7a795df5bcb4d24a863c69937794fc7"}, "dist": {"type": "zip", "url": "https://biz-satis-test.kuainiujinke.com/composer/dist/xlerr/kvmanager/xlerr-kvmanager-4.15.0-a56e30.zip", "reference": "5c5dc2fba7a795df5bcb4d24a863c69937794fc7"}, "require": {"codingheping/code-diff": "^1.0", "ext-json": "*", "ext-simplexml": "*", "nesbot/carbon": "^2.44", "php": "^7.4 || ^8.0", "symfony/yaml": ">=2.7 <6.0", "waterank/audit": "^3.4", "xlerr/httpca": "^1.2", "xlerr/jasypt": "^1.0", "xlerr/task": "^2.1", "xlerr/yii2-adminlte": "^2.1 || ^3.0", "xlerr/yii2-common-widgets": ">=1.13.0 <2.0", "xlerr/yii2-widget-code-editor": "^1.0", "yiisoft/yii2": "^2.0"}, "require-dev": {"roave/security-advisories": "dev-latest", "yiisoft/yii2-debug": "~2.0.0"}, "type": "yii2-extension", "autoload": {"psr-4": {"kvmanager\\": "src/"}}, "autoload-dev": {"psr-4": {"kvmanager\\test\\": "test/"}, "classmap": ["dev_lib.php"]}, "license": ["MIT"], "authors": [{"name": "xlerr", "email": "<EMAIL>"}], "time": "2025-02-10T08:21:15+00:00"}, {"name": "xlerr/lock", "version": "1.0.1", "source": {"type": "git", "url": "************************:xlerr/lock.git", "reference": "f5e05e777590e8245a741066771360bfdfb2181d"}, "dist": {"type": "zip", "url": "https://biz-satis-test.kuainiujinke.com/composer/dist/xlerr/lock/xlerr-lock-1.0.1-10e465.zip", "reference": "f5e05e777590e8245a741066771360bfdfb2181d"}, "require": {"php": ">= 7.1", "symfony/polyfill-uuid": "^1.0"}, "type": "library", "autoload": {"psr-4": {"xlerr\\lock\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "xlerr", "email": "<EMAIL>"}], "description": "redis lock", "time": "2022-04-26T09:04:30+00:00"}, {"name": "xlerr/metric", "version": "0.0.12", "source": {"type": "git", "url": "************************:xlerr/metric.git", "reference": "a3c7b9ede338ca70012f8a152a1e621a874f8d21"}, "dist": {"type": "zip", "url": "https://biz-satis-test.kuainiujinke.com/composer/dist/xlerr/metric/xlerr-metric-0.0.12-3906dd.zip", "reference": "a3c7b9ede338ca70012f8a152a1e621a874f8d21"}, "require": {"ext-json": "*", "nesbot/carbon": "^2.0", "php": ">=7.4", "waterank/audit": ">= ********", "xlerr/yii2-common-widgets": "^1.5", "yiisoft/yii2": ">=2.0.13 <3.0"}, "require-dev": {"yiisoft/yii2-debug": "~2.0.0"}, "type": "yii2-extension", "autoload": {"psr-4": {"Xlerr\\Metric\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "heping", "email": "<EMAIL>"}], "description": "description", "time": "2025-03-19T08:40:45+00:00"}, {"name": "xlerr/sentry-television", "version": "1.0.0", "source": {"type": "git", "url": "************************:xlerr/sentry-television.git", "reference": "f6e4271e15dad4e7f59d5a14f9a213f6c3da37ed"}, "dist": {"type": "zip", "url": "https://biz-satis-test.kuainiujinke.com/composer/dist/xlerr/sentry-television/xlerr-sentry-television-1.0.0-31b505.zip", "reference": "f6e4271e15dad4e7f59d5a14f9a213f6c3da37ed"}, "require": {"guzzlehttp/guzzle": "^6.3 || ^7.0", "php": "^7.1 || ^8.0", "yiisoft/yii2": "^2.0"}, "type": "library", "autoload": {"psr-4": {"xlerr\\sentry\\television\\": "src/"}}, "autoload-dev": {"psr-4": {"xlerr\\sentry\\television\\test\\": "test/"}}, "license": ["MIT"], "authors": [{"name": "xlerr", "email": "<EMAIL>"}], "time": "2023-06-30T10:47:22+00:00"}, {"name": "xlerr/settlement-flow", "version": "3.8.5", "source": {"type": "git", "url": "************************:xlerr/settlement-flow.git", "reference": "cee6d3e53f48f957c6f06179207e950d4629718a"}, "dist": {"type": "zip", "url": "https://biz-satis-test.kuainiujinke.com/composer/dist/xlerr/settlement-flow/xlerr-settlement-flow-3.8.5-bc913f.zip", "reference": "cee6d3e53f48f957c6f06179207e950d4629718a"}, "require": {"nesbot/carbon": "^2.58", "php": "^7.4 || ^8.0 || ^8.1", "predis/predis": "^2.1", "waterank/audit": "^3.0", "xlerr/cpop-income": "~0.1.0", "xlerr/datasource": "^4.4", "xlerr/desensitise": "^2.0", "xlerr/diy-report": "^0.0", "xlerr/httpca": "^1.2", "xlerr/kvmanager": "^4.3", "xlerr/lock": "^1.0", "xlerr/task": "^2.0", "xlerr/yii2-adminlte": "^2.1 || ^3.0", "xlerr/yii2-common-widgets": ">=1.18.6 < 2.0", "xlerr/yii2-widget-code-editor": "^1.1", "yiisoft/yii2": "^2.0", "yiisoft/yii2-bootstrap": "^2.0"}, "require-dev": {"roave/security-advisories": "dev-latest", "symfony/var-dumper": "^5.4", "yiisoft/yii2-debug": "^2.1", "yiisoft/yii2-gii": "^2.2"}, "type": "yii2-extension", "autoload": {"psr-4": {"Xlerr\\SettlementFlow\\": "src/"}}, "autoload-dev": {"psr-4": {"Xlerr\\SettlementFlowTests\\": "tests/"}}, "license": ["MIT"], "authors": [{"name": "xlerr", "email": "<EMAIL>"}], "description": "结算流程系统", "time": "2025-07-24T09:18:40+00:00"}, {"name": "xlerr/task", "version": "2.7.0", "source": {"type": "git", "url": "************************:xlerr/task.git", "reference": "3ac5baeeacce35dde5874a77e0f2d1a4a5948e36"}, "dist": {"type": "zip", "url": "https://biz-satis-test.kuainiujinke.com/composer/dist/xlerr/task/xlerr-task-2.7.0-3700c6.zip", "reference": "3ac5baeeacce35dde5874a77e0f2d1a4a5948e36"}, "require": {"ext-json": "*", "nesbot/carbon": "^2.0", "php": ">=7.4", "xlerr/yii2-common-widgets": "^1.0", "xlerr/yii2-widget-code-editor": "^1.0", "yiisoft/yii2": ">=2.0.13 <3.0"}, "type": "yii2-extension", "autoload": {"psr-4": {"xlerr\\task\\": "src"}}, "license": ["MIT"], "authors": [{"name": "xlerr", "email": "<EMAIL>"}], "description": "yii2 task", "time": "2024-07-18T02:11:14+00:00"}, {"name": "xlerr/yii2-adminlte", "version": "3.1.3", "source": {"type": "git", "url": "************************:xlerr/yii2-adminlte.git", "reference": "f45c85a04953d28605a394862ec3870a1e1ae734"}, "dist": {"type": "zip", "url": "https://biz-satis-test.kuainiujinke.com/composer/dist/xlerr/yii2-adminlte/xlerr-yii2-adminlte-3.1.3-07ba52.zip", "reference": "f45c85a04953d28605a394862ec3870a1e1ae734"}, "require": {"almasaeed2010/adminlte": "^2.4.0", "components/font-awesome": "^5.0", "ext-json": "*", "php": "^7.4 || ^8.0", "yiisoft/yii2": ">=2.0.13", "yiisoft/yii2-bootstrap": "^2.0"}, "require-dev": {"roave/security-advisories": "dev-latest", "yiisoft/yii2-debug": "^2.1"}, "type": "project", "autoload": {"psr-4": {"xlerr\\adminlte\\": "src/"}, "files": ["src/functions.php"]}, "license": ["MIT"], "authors": [{"name": "xlerr", "email": "<EMAIL>"}], "description": "tab menu for AdminLTE", "keywords": ["AdminLTE", "iframe", "tab menu"], "support": {"source": "https://git.kuainiujinke.com/xlerr/yii2-adminlte.git"}, "time": "2024-03-28T09:24:39+00:00"}, {"name": "xlerr/yii2-common-widgets", "version": "1.18.13", "source": {"type": "git", "url": "************************:xlerr/yii2-common-widgets.git", "reference": "740d41839a77b208a8dd3cf3159f291d0051100e"}, "dist": {"type": "zip", "url": "https://biz-satis-test.kuainiujinke.com/composer/dist/xlerr/yii2-common-widgets/xlerr-yii2-common-widgets-1.18.13-db2a58.zip", "reference": "740d41839a77b208a8dd3cf3159f291d0051100e"}, "require": {"guzzlehttp/guzzle": "^6.0 || ^7.0", "kartik-v/yii2-date-range": "*", "kartik-v/yii2-widgets": "^3.4", "npm-asset/highlightjs--cdn-assets": "^11.11", "npm-asset/lodash": "^4.17", "npm-asset/moment": "^2.30", "npm-asset/showdown": "^2.1", "npm-asset/sortablejs": "~1.14.0", "npm-asset/vue": "^3.5", "yiisoft/yii2": "^2.0"}, "type": "library", "autoload": {"psr-4": {"xlerr\\common\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "xlerr", "email": "<EMAIL>"}], "time": "2025-07-29T03:12:22+00:00"}, {"name": "xlerr/yii2-widget-code-editor", "version": "1.1.9", "source": {"type": "git", "url": "************************:xlerr/yii2-widget-code-editor.git", "reference": "5e48229430d040cfbc0cbd0c0df362afc6579980"}, "dist": {"type": "zip", "url": "https://biz-satis-test.kuainiujinke.com/composer/dist/xlerr/yii2-widget-code-editor/xlerr-yii2-widget-code-editor-1.1.9-fd1995.zip", "reference": "5e48229430d040cfbc0cbd0c0df362afc6579980"}, "require": {"npm-asset/ace-builds": "~1.4.12", "yiisoft/yii2": "^2.0"}, "type": "library", "autoload": {"psr-4": {"xlerr\\CodeEditor\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "xlerr", "email": "<EMAIL>"}], "description": "code editor", "keywords": ["ace", "editor", "yii2"], "time": "2025-07-28T04:19:13+00:00"}, {"name": "yiisoft/yii2", "version": "2.0.53", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-framework.git", "reference": "6c622fb8243181d7912b62ad80821cc0e1c745db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-framework/zipball/6c622fb8243181d7912b62ad80821cc0e1c745db", "reference": "6c622fb8243181d7912b62ad80821cc0e1c745db", "shasum": ""}, "require": {"bower-asset/inputmask": "^5.0.8 ", "bower-asset/jquery": "3.7.*@stable | 3.6.*@stable | 3.5.*@stable | 3.4.*@stable | 3.3.*@stable | 3.2.*@stable | 3.1.*@stable | 2.2.*@stable | 2.1.*@stable | 1.11.*@stable | 1.12.*@stable", "bower-asset/punycode": "^1.4", "bower-asset/yii2-pjax": "~2.0.1", "cebe/markdown": "~1.0.0 | ~1.1.0 | ~1.2.0", "ext-ctype": "*", "ext-mbstring": "*", "ezyang/htmlpurifier": "^4.17", "lib-pcre": "*", "php": ">=7.3.0", "yiisoft/yii2-composer": "~2.0.4"}, "bin": ["yii"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.yiiframework.com/", "role": "Founder and project lead"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://rmcreative.ru/", "role": "Core framework development"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "http://mdomba.info/", "role": "Core framework development"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.cebe.cc/", "role": "Core framework development"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://resurtm.com/", "role": "Core framework development"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Core framework development"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Core framework development"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://dynasource.eu", "role": "Core framework development"}], "description": "Yii PHP Framework Version 2", "homepage": "https://www.yiiframework.com/", "keywords": ["framework", "yii2"], "support": {"forum": "https://forum.yiiframework.com/", "irc": "ircs://irc.libera.chat:6697/yii", "issues": "https://github.com/yiisoft/yii2/issues?state=open", "source": "https://github.com/yiisoft/yii2", "wiki": "https://www.yiiframework.com/wiki"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2", "type": "tidelift"}], "time": "2025-06-27T07:42:53+00:00"}, {"name": "yiisoft/yii2-authclient", "version": "2.2.15", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-authclient.git", "reference": "a11cbb065a2424c25f9dc0d0df6852d605d869b2"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/yiisoft/yii2-authclient/2.2.15/yiisoft-yii2-authclient-2.2.15.zip", "reference": "a11cbb065a2424c25f9dc0d0df6852d605d869b2", "shasum": ""}, "require": {"paragonie/random_compat": ">=1", "yiisoft/yii2": "~2.0.13", "yiisoft/yii2-httpclient": "~2.0.5"}, "require-dev": {"cweagans/composer-patches": "^1.7", "phpunit/phpunit": "4.8.34"}, "suggest": {"web-token/jwt-checker": "required for JWS, JWT or JWK related flows like OpenIDConnect", "web-token/jwt-key-mgmt": "required for JWS, JWT or JWK related flows like OpenIDConnect", "web-token/jwt-signature": "required for JWS, JWT or JWK related flows like OpenIDConnect", "web-token/jwt-signature-algorithm-ecdsa": "required for JWS, JWT or JWK related flows like OpenIDConnect", "web-token/jwt-signature-algorithm-hmac": "required for JWS, JWT or JWK related flows like OpenIDConnect", "web-token/jwt-signature-algorithm-rsa": "required for JWS, JWT or JWK related flows like OpenIDConnect"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}, "patches": {"phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_mock_objects.patch"}, "phpunit/phpunit": {"Fix PHP 7 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php7.patch", "Fix PHP 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php8.patch"}}}, "autoload": {"psr-4": {"yii\\authclient\\": "src"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "External authentication via OAuth and OpenID for the Yii framework", "keywords": ["OpenID Connect", "OpenId", "api", "auth", "o<PERSON>h", "yii2"], "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-authclient", "type": "tidelift"}], "time": "2023-12-16T15:14:16+00:00"}, {"name": "yiisoft/yii2-bootstrap", "version": "2.0.11", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-bootstrap.git", "reference": "83d144f4089adaa7064ad60dc4c1436daa2eb30e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-bootstrap/zipball/83d144f4089adaa7064ad60dc4c1436daa2eb30e", "reference": "83d144f4089adaa7064ad60dc4c1436daa2eb30e", "shasum": ""}, "require": {"bower-asset/bootstrap": "3.4.* | 3.3.* | 3.2.* | 3.1.*", "yiisoft/yii2": "~2.0.6"}, "require-dev": {"cweagans/composer-patches": "^1.7", "phpunit/phpunit": "4.8.34"}, "type": "yii2-extension", "extra": {"patches": {"phpunit/phpunit": {"Fix PHP 7 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php7.patch", "Fix PHP 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php8.patch"}, "phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_mock_objects.patch"}}, "branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\bootstrap\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.yiiframework.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://rmcreative.ru/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Twitter Bootstrap extension for the Yii framework", "keywords": ["bootstrap", "yii2"], "support": {"forum": "http://www.yiiframework.com/forum/", "irc": "irc://irc.freenode.net/yii", "issues": "https://github.com/yiisoft/yii2-bootstrap/issues", "source": "https://github.com/yiisoft/yii2-bootstrap", "wiki": "http://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-bootstrap", "type": "tidelift"}], "time": "2021-08-09T20:54:06+00:00"}, {"name": "yiisoft/yii2-composer", "version": "2.0.11", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-composer.git", "reference": "b684b01ecb119c8287721def726a0e24fec2fef2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-composer/zipball/b684b01ecb119c8287721def726a0e24fec2fef2", "reference": "b684b01ecb119c8287721def726a0e24fec2fef2", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 | ^2.0"}, "require-dev": {"composer/composer": "^1.0 | ^2.0@dev", "phpunit/phpunit": "<7"}, "type": "composer-plugin", "extra": {"class": "yii\\composer\\Plugin", "branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\composer\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The composer plugin for Yii extension installer", "keywords": ["composer", "extension installer", "yii2"], "support": {"forum": "https://www.yiiframework.com/forum/", "irc": "ircs://irc.libera.chat:6697/yii", "issues": "https://github.com/yiisoft/yii2-composer/issues", "source": "https://github.com/yiisoft/yii2-composer", "wiki": "https://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-composer", "type": "tidelift"}], "time": "2025-02-13T20:59:36+00:00"}, {"name": "yiisoft/yii2-httpclient", "version": "2.0.15", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-httpclient.git", "reference": "5a8350e15f2db3555ba52830c9c701587c136e87"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/yiisoft/yii2-httpclient/2.0.15/yiisoft-yii2-httpclient-2.0.15.zip", "reference": "5a8350e15f2db3555ba52830c9c701587c136e87", "shasum": ""}, "require": {"paragonie/random_compat": ">=1", "yiisoft/yii2": "~2.0.13"}, "require-dev": {"cweagans/composer-patches": "^1.7", "phpunit/phpunit": "4.8.34"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}, "composer-exit-on-patch-failure": true, "patches": {"phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_mock_objects.patch"}, "phpunit/phpunit": {"Fix PHP 7 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php7.patch", "Fix PHP 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php8.patch"}}}, "autoload": {"psr-4": {"yii\\httpclient\\": "src"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "HTTP client extension for the Yii framework", "keywords": ["curl", "http", "httpclient", "yii2"], "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-httpclient", "type": "tidelift"}], "time": "2023-05-22T18:32:24+00:00"}, {"name": "yiisoft/yii2-symfonymailer", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-symfonymailer.git", "reference": "291c00979a9bf14e89bb5230ddd700a6dc130e51"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-symfonymailer/zipball/291c00979a9bf14e89bb5230ddd700a6dc130e51", "reference": "291c00979a9bf14e89bb5230ddd700a6dc130e51", "shasum": ""}, "require": {"php": ">=7.4.0", "symfony/mailer": ">=5.4.0", "yiisoft/yii2": ">=2.0.4"}, "require-dev": {"captainhook/captainhook": "^5.10", "captainhook/plugin-composer": "^5.3", "phpunit/phpunit": "9.5.10", "symplify/easy-coding-standard": "^10.1", "vimeo/psalm": "^4.22"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\symfonymailer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The SymfonyMailer integration for the Yii framework", "keywords": ["email", "mail", "mailer", "symfony", "symfonymailer", "yii2"], "support": {"forum": "http://www.yiiframework.com/forum/", "irc": "irc://irc.freenode.net/yii", "issues": "https://github.com/yiisoft/yii2-symfonymailer/issues", "source": "https://github.com/yiisoft/yii2-symfonymailer", "wiki": "http://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-symfonymailer", "type": "tidelift"}], "time": "2022-12-05T08:20:13+00:00"}], "packages-dev": [{"name": "fakerphp/faker", "version": "v1.22.0", "source": {"type": "git", "url": "https://github.com/FakerPHP/Faker.git", "reference": "f85772abd508bd04e20bb4b1bbe260a68d0066d2"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/fakerphp/faker/v1.22.0/fakerphp-faker-v1.22.0.zip", "reference": "f85772abd508bd04e20bb4b1bbe260a68d0066d2", "shasum": ""}, "require": {"php": "^7.4 || ^8.0", "psr/container": "^1.0 || ^2.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "conflict": {"fzaninotto/faker": "*"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "doctrine/persistence": "^1.3 || ^2.0", "ext-intl": "*", "phpunit/phpunit": "^9.5.26", "symfony/phpunit-bridge": "^5.4.16"}, "suggest": {"doctrine/orm": "Required to use Faker\\ORM\\Doctrine", "ext-curl": "Required by Faker\\Provider\\Image to download images.", "ext-dom": "Required by Faker\\Provider\\HtmlLorem for generating random HTML.", "ext-iconv": "Required by Faker\\Provider\\ru_RU\\Text::realText() for generating real Russian text.", "ext-mbstring": "Required for multibyte Unicode string functionality."}, "type": "library", "extra": {"branch-alias": {"dev-main": "v1.21-dev"}}, "autoload": {"psr-4": {"Faker\\": "src/Faker/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Faker is a PHP library that generates fake data for you.", "keywords": ["data", "faker", "fixtures"], "time": "2023-05-14T12:31:37+00:00"}, {"name": "phpspec/php-diff", "version": "v1.1.3", "source": {"type": "git", "url": "https://github.com/phpspec/php-diff.git", "reference": "fc1156187f9f6c8395886fe85ed88a0a245d72e9"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/phpspec/php-diff/v1.1.3/phpspec-php-diff-v1.1.3.zip", "reference": "fc1156187f9f6c8395886fe85ed88a0a245d72e9", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-0": {"Diff": "lib/"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "homepage": "http://github.com/chrisboulton"}], "description": "A comprehensive library for generating differences between two hashable objects (strings or arrays).", "time": "2020-09-18T13:47:07+00:00"}, {"name": "roave/security-advisories", "version": "dev-latest", "source": {"type": "git", "url": "https://github.com/Roave/SecurityAdvisories.git", "reference": "0b1f127d644a2ff8af1c8bd2955dbe3c11812017"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Roave/SecurityAdvisories/zipball/0b1f127d644a2ff8af1c8bd2955dbe3c11812017", "reference": "0b1f127d644a2ff8af1c8bd2955dbe3c11812017", "shasum": ""}, "conflict": {"3f/pygmentize": "<1.2", "adaptcms/adaptcms": "<=1.3", "admidio/admidio": "<4.3.12", "adodb/adodb-php": "<=5.22.8", "aheinze/cockpit": "<2.2", "aimeos/ai-admin-graphql": ">=2022.04.1,<2022.10.10|>=2023.04.1,<2023.10.6|>=2024.04.1,<2024.07.2", "aimeos/ai-admin-jsonadm": "<2020.10.13|>=2021.04.1,<2021.10.6|>=2022.04.1,<2022.10.3|>=2023.04.1,<2023.10.4|==2024.04.1", "aimeos/ai-client-html": ">=2020.04.1,<2020.10.27|>=2021.04.1,<2021.10.22|>=2022.04.1,<2022.10.13|>=2023.04.1,<2023.10.15|>=2024.04.1,<2024.04.7", "aimeos/ai-controller-frontend": "<2020.10.15|>=2021.04.1,<2021.10.8|>=2022.04.1,<2022.10.8|>=2023.04.1,<2023.10.9|==2024.04.1", "aimeos/aimeos-core": ">=2022.04.1,<2022.10.17|>=2023.04.1,<2023.10.17|>=2024.04.1,<2024.04.7", "aimeos/aimeos-typo3": "<19.10.12|>=20,<20.10.5", "airesvsg/acf-to-rest-api": "<=3.1", "akaunting/akaunting": "<2.1.13", "akeneo/pim-community-dev": "<5.0.119|>=6,<6.0.53", "alextselegidis/easyappointments": "<=1.5.1", "alterphp/easyadmin-extension-bundle": ">=1.2,<1.2.11|>=1.3,<1.3.1", "amazing/media2click": ">=1,<1.3.3", "ameos/ameos_tarteaucitron": "<1.2.23", "amphp/artax": "<1.0.6|>=2,<2.0.6", "amphp/http": "<=1.7.2|>=2,<=2.1", "amphp/http-client": ">=4,<4.4", "anchorcms/anchor-cms": "<=0.12.7", "andreapollastri/cipi": "<=3.1.15", "andrewhaine/silverstripe-form-capture": ">=0.2,<=0.2.3|>=1,<1.0.2|>=2,<2.2.5", "aoe/restler": "<1.7.1", "apache-solr-for-typo3/solr": "<2.8.3", "apereo/phpcas": "<1.6", "api-platform/core": "<3.4.17|>=*******-alpha1,<4.0.22", "api-platform/graphql": "<3.4.17|>=*******-alpha1,<4.0.22", "appwrite/server-ce": "<=1.2.1", "arc/web": "<3", "area17/twill": "<1.2.5|>=2,<2.5.3", "artesaos/seotools": "<0.17.2", "asymmetricrypt/asymmetricrypt": "<9.9.99", "athlon1600/php-proxy": "<=5.1", "athlon1600/php-proxy-app": "<=3", "athlon1600/youtube-downloader": "<=4", "austintoddj/canvas": "<=3.4.2", "auth0/auth0-php": ">=*******-beta1,<8.14", "auth0/login": "<7.17", "auth0/symfony": "<5.4", "auth0/wordpress": "<5.3", "automad/automad": "<*******-alpha5", "automattic/jetpack": "<9.8", "awesome-support/awesome-support": "<=6.0.7", "aws/aws-sdk-php": "<3.288.1", "azuracast/azuracast": "<0.18.3", "b13/seo_basics": "<0.8.2", "backdrop/backdrop": "<1.27.3|>=1.28,<1.28.2", "backpack/crud": "<3.4.9", "backpack/filemanager": "<2.0.2|>=3,<3.0.9", "bacula-web/bacula-web": "<*******-RC2-dev", "badaso/core": "<2.7", "bagisto/bagisto": "<2.1", "barrelstrength/sprout-base-email": "<1.2.7", "barrelstrength/sprout-forms": "<3.9", "barryvdh/laravel-translation-manager": "<0.6.8", "barzahlen/barzahlen-php": "<2.0.1", "baserproject/basercms": "<=5.1.1", "bassjobsen/bootstrap-3-typeahead": ">4.0.2", "bbpress/bbpress": "<2.6.5", "bcit-ci/codeigniter": "<3.1.3", "bcosca/fatfree": "<3.7.2", "bedita/bedita": "<4", "bednee/cooluri": "<1.0.30", "bigfork/silverstripe-form-capture": ">=3,<3.1.1", "billz/raspap-webgui": "<3.3.6", "binarytorch/larecipe": "<2.8.1", "bk2k/bootstrap-package": ">=7.1,<7.1.2|>=8,<8.0.8|>=9,<9.0.4|>=9.1,<9.1.3|>=10,<10.0.10|>=11,<11.0.3", "blueimp/jquery-file-upload": "==6.4.4", "bmarshall511/wordpress_zero_spam": "<5.2.13", "bolt/bolt": "<3.7.2", "bolt/core": "<=4.2", "born05/craft-twofactorauthentication": "<3.3.4", "bottelet/flarepoint": "<2.2.1", "bref/bref": "<2.1.17", "brightlocal/phpwhois": "<=4.2.5", "brotkrueml/codehighlight": "<2.7", "brotkrueml/schema": "<1.13.1|>=2,<2.5.1", "brotkrueml/typo3-matomo-integration": "<1.3.2", "buddypress/buddypress": "<7.2.1", "bugsnag/bugsnag-laravel": ">=2,<2.0.2", "bvbmedia/multishop": "<2.0.39", "bytefury/crater": "<6.0.2", "cachethq/cachet": "<2.5.1", "cakephp/cakephp": "<3.10.3|>=4,<4.0.10|>=4.1,<4.1.4|>=4.2,<4.2.12|>=4.3,<4.3.11|>=4.4,<4.4.10", "cakephp/database": ">=4.2,<4.2.12|>=4.3,<4.3.11|>=4.4,<4.4.10", "cardgate/magento2": "<2.0.33", "cardgate/woocommerce": "<=3.1.15", "cart2quote/module-quotation": ">=4.1.6,<=4.4.5|>=5,<5.4.4", "cart2quote/module-quotation-encoded": ">=4.1.6,<=4.4.5|>=5,<5.4.4", "cartalyst/sentry": "<=2.1.6", "catfan/medoo": "<1.7.5", "causal/oidc": "<4", "cecil/cecil": "<7.47.1", "centreon/centreon": "<22.10.15", "cesnet/simplesamlphp-module-proxystatistics": "<3.1", "chriskacerguis/codeigniter-restserver": "<=2.7.1", "chrome-php/chrome": "<1.14", "civicrm/civicrm-core": ">=4.2,<4.2.9|>=4.3,<4.3.3", "ckeditor/ckeditor": "<4.25", "clickstorm/cs-seo": ">=6,<6.8|>=7,<7.5|>=8,<8.4|>=9,<9.3", "co-stack/fal_sftp": "<0.2.6", "cockpit-hq/cockpit": "<2.11.4", "codeception/codeception": "<3.1.3|>=4,<4.1.22", "codeigniter/framework": "<3.1.10", "codeigniter4/framework": "<4.5.8", "codeigniter4/shield": "<*******-beta8", "codiad/codiad": "<=2.8.4", "codingms/additional-tca": ">=1.7,<1.15.17|>=1.16,<1.16.9", "commerceteam/commerce": ">=0.9.6,<0.9.9", "components/jquery": ">=1.0.3,<3.5", "composer/composer": "<1.10.27|>=2,<2.2.24|>=2.3,<2.7.7", "concrete5/concrete5": "<9.4.0.0-RC2-dev", "concrete5/core": "<8.5.8|>=9,<9.1", "contao-components/mediaelement": ">=2.14.2,<2.21.1", "contao/comments-bundle": ">=2,<4.13.40|>=*******-RC1-dev,<5.3.4", "contao/contao": ">=3,<3.5.37|>=4,<4.4.56|>=4.5,<4.9.40|>=4.10,<4.11.7|>=4.13,<4.13.21|>=5.1,<5.1.4", "contao/core": "<3.5.39", "contao/core-bundle": "<4.13.54|>=5,<5.3.30|>=5.4,<5.5.6", "contao/listing-bundle": ">=3,<=3.5.30|>=4,<4.4.8", "contao/managed-edition": "<=1.5", "corveda/phpsandbox": "<1.3.5", "cosenary/instagram": "<=2.3", "couleurcitron/tarteaucitron-wp": "<0.3", "craftcms/cms": "<4.15.3|>=5,<5.7.5", "croogo/croogo": "<4", "cuyz/valinor": "<0.12", "czim/file-handling": "<1.5|>=2,<2.3", "czproject/git-php": "<4.0.3", "damienharper/auditor-bundle": "<5.2.6", "dapphp/securimage": "<3.6.6", "darylldoyle/safe-svg": "<1.9.10", "datadog/dd-trace": ">=0.30,<0.30.2", "datatables/datatables": "<1.10.10", "david-garcia/phpwhois": "<=4.3.1", "dbrisinajumi/d2files": "<1", "dcat/laravel-admin": "<=2.1.3|==*******-beta|==*******-beta", "derhansen/fe_change_pwd": "<2.0.5|>=3,<3.0.3", "derhansen/sf_event_mgt": "<4.3.1|>=5,<5.1.1|>=7,<7.4", "desperado/xml-bundle": "<=0.1.7", "dev-lancer/minecraft-motd-parser": "<=1.0.5", "devgroup/dotplant": "<2020.09.14-dev", "digimix/wp-svg-upload": "<=1", "directmailteam/direct-mail": "<6.0.3|>=7,<7.0.3|>=8,<9.5.2", "dl/yag": "<3.0.1", "dmk/webkitpdf": "<1.1.4", "dnadesign/silverstripe-elemental": "<5.3.12", "doctrine/annotations": "<1.2.7", "doctrine/cache": ">=1,<1.3.2|>=1.4,<1.4.2", "doctrine/common": "<2.4.3|>=2.5,<2.5.1", "doctrine/dbal": ">=2,<2.0.8|>=2.1,<2.1.2|>=3,<3.1.4", "doctrine/doctrine-bundle": "<1.5.2", "doctrine/doctrine-module": "<0.7.2", "doctrine/mongodb-odm": "<1.0.2", "doctrine/mongodb-odm-bundle": "<3.0.1", "doctrine/orm": ">=1,<1.2.4|>=2,<2.4.8|>=2.5,<2.5.1|>=2.8.3,<2.8.4", "dolibarr/dolibarr": "<=21.0.2", "dompdf/dompdf": "<2.0.4", "doublethreedigital/guest-entries": "<3.1.2", "drupal/admin_audit_trail": "<1.0.5", "drupal/ai": "<1.0.5", "drupal/alogin": "<2.0.6", "drupal/cache_utility": "<1.2.1", "drupal/commerce_alphabank_redirect": "<1.0.3", "drupal/commerce_eurobank_redirect": "<2.1.1", "drupal/config_split": "<1.10|>=2,<2.0.2", "drupal/core": ">=6,<6.38|>=7,<7.102|>=8,<10.3.14|>=10.4,<10.4.5|>=11,<11.0.13|>=11.1,<11.1.5", "drupal/core-recommended": ">=7,<7.102|>=8,<10.2.11|>=10.3,<10.3.9|>=11,<11.0.8", "drupal/drupal": ">=5,<5.11|>=6,<6.38|>=7,<7.102|>=8,<10.2.11|>=10.3,<10.3.9|>=11,<11.0.8", "drupal/formatter_suite": "<2.1", "drupal/gdpr": "<3.0.1|>=3.1,<3.1.2", "drupal/google_tag": "<1.8|>=2,<2.0.8", "drupal/ignition": "<1.0.4", "drupal/lightgallery": "<1.6", "drupal/link_field_display_mode_formatter": "<1.6", "drupal/matomo": "<1.24", "drupal/oauth2_client": "<4.1.3", "drupal/oauth2_server": "<2.1", "drupal/obfuscate": "<2.0.1", "drupal/quick_node_block": "<2", "drupal/rapidoc_elements_field_formatter": "<1.0.1", "drupal/spamspan": "<3.2.1", "drupal/tfa": "<1.10", "duncanmcclean/guest-entries": "<3.1.2", "dweeves/magmi": "<=0.7.24", "ec-cube/ec-cube": "<2.4.4|>=2.11,<=2.17.1|>=3,<=3.0.18.0-patch4|>=4,<=4.1.2", "ecodev/newsletter": "<=4", "ectouch/ectouch": "<=2.7.2", "egroupware/egroupware": "<23.1.20240624", "elefant/cms": "<2.0.7", "elgg/elgg": "<3.3.24|>=4,<4.0.5", "elijaa/phpmemcacheadmin": "<=1.3", "elmsln/haxcms": "<11.0.8", "encore/laravel-admin": "<=1.8.19", "endroid/qr-code-bundle": "<3.4.2", "enhavo/enhavo-app": "<=0.13.1", "enshrined/svg-sanitize": "<0.15", "erusev/parsedown": "<1.7.2", "ether/logs": "<3.0.4", "evolutioncms/evolution": "<=3.2.3", "exceedone/exment": "<4.4.3|>=5,<5.0.3", "exceedone/laravel-admin": "<2.2.3|==3", "ezsystems/demobundle": ">=5.4,<5.4.6.1-dev", "ezsystems/ez-support-tools": ">=2.2,<2.2.3", "ezsystems/ezdemo-ls-extension": ">=5.4,<5.4.2.1-dev", "ezsystems/ezfind-ls": ">=5.3,<5.3.6.1-dev|>=5.4,<5.4.11.1-dev|>=2017.12,<2017.12.0.1-dev", "ezsystems/ezplatform": "<=1.13.6|>=2,<=2.5.24", "ezsystems/ezplatform-admin-ui": ">=1.3,<1.3.5|>=1.4,<1.4.6|>=1.5,<1.5.29|>=2.3,<2.3.38|>=3.3,<3.3.39", "ezsystems/ezplatform-admin-ui-assets": ">=4,<4.2.1|>=5,<5.0.1|>=5.1,<5.1.1|>=5.3.0.0-beta1,<5.3.5", "ezsystems/ezplatform-graphql": ">=*******-RC1-dev,<1.0.13|>=*******-beta1,<2.3.12", "ezsystems/ezplatform-http-cache": "<2.3.16", "ezsystems/ezplatform-kernel": "<1.2.5.1-dev|>=1.3,<1.3.35", "ezsystems/ezplatform-rest": ">=1.2,<=1.2.2|>=1.3,<1.3.8", "ezsystems/ezplatform-richtext": ">=2.3,<2.3.26|>=3.3,<3.3.40", "ezsystems/ezplatform-solr-search-engine": ">=1.7,<1.7.12|>=2,<2.0.2|>=3.3,<3.3.15", "ezsystems/ezplatform-user": ">=1,<1.0.1", "ezsystems/ezpublish-kernel": "<********-dev|>=7,<7.5.31", "ezsystems/ezpublish-legacy": "<=2017.12.7.3|>=2018.6,<=2019.03.5.1", "ezsystems/platform-ui-assets-bundle": ">=4.2,<4.2.3", "ezsystems/repository-forms": ">=2.3,<*******-dev|>=2.5,<2.5.15", "ezyang/htmlpurifier": "<=4.2", "facade/ignition": "<1.16.15|>=2,<2.4.2|>=2.5,<2.5.2", "facturascripts/facturascripts": "<=2022.08", "fastly/magento2": "<1.2.26", "feehi/cms": "<=2.1.1", "feehi/feehicms": "<=2.1.1", "fenom/fenom": "<=2.12.1", "filament/actions": ">=3.2,<3.2.123", "filament/infolists": ">=3,<3.2.115", "filament/tables": ">=3,<3.2.115", "filegator/filegator": "<7.8", "filp/whoops": "<2.1.13", "fineuploader/php-traditional-server": "<=1.2.2", "firebase/php-jwt": "<6", "fisharebest/webtrees": "<=2.1.18", "fixpunkt/fp-masterquiz": "<2.2.1|>=3,<3.5.2", "fixpunkt/fp-newsletter": "<1.1.1|>=1.2,<2.1.2|>=2.2,<3.2.6", "flarum/core": "<1.8.10", "flarum/flarum": "<*******-beta8", "flarum/framework": "<1.8.10", "flarum/mentions": "<1.6.3", "flarum/sticky": ">=*******-beta14,<=*******-beta15", "flarum/tags": "<=*******-beta13", "floriangaerber/magnesium": "<0.3.1", "fluidtypo3/vhs": "<5.1.1", "fof/byobu": ">=*******-beta2,<1.1.7", "fof/upload": "<1.2.3", "foodcoopshop/foodcoopshop": ">=3.2,<3.6.1", "fooman/tcpdf": "<6.2.22", "forkcms/forkcms": "<5.11.1", "fossar/tcpdf-parser": "<6.2.22", "francoisjacquet/rosariosis": "<=11.5.1", "frappant/frp-form-answers": "<3.1.2|>=4,<4.0.2", "friendsofsymfony/oauth2-php": "<1.3", "friendsofsymfony/rest-bundle": ">=1.2,<1.2.2", "friendsofsymfony/user-bundle": ">=1,<1.3.5", "friendsofsymfony1/swiftmailer": ">=4,<5.4.13|>=6,<6.2.5", "friendsofsymfony1/symfony1": ">=1.1,<1.5.19", "friendsoftypo3/mediace": ">=7.6.2,<7.6.5", "friendsoftypo3/openid": ">=4.5,<4.5.31|>=4.7,<4.7.16|>=6,<6.0.11|>=6.1,<6.1.6", "froala/wysiwyg-editor": "<=4.3", "froxlor/froxlor": "<=2.2.5", "frozennode/administrator": "<=5.0.12", "fuel/core": "<1.8.1", "funadmin/funadmin": "<=5.0.2", "gaoming13/wechat-php-sdk": "<=1.10.2", "genix/cms": "<=1.1.11", "georgringer/news": "<1.3.3", "geshi/geshi": "<=1.0.9.1", "getformwork/formwork": "<1.13.1|>=*******-beta1,<*******-beta4", "getgrav/grav": "<1.7.46", "getkirby/cms": "<3.9.8.3-dev|>=3.10,<3.10.1.2-dev|>=4,<4.7.1", "getkirby/kirby": "<3.9.8.3-dev|>=3.10,<3.10.1.2-dev|>=4,<4.7.1", "getkirby/panel": "<2.5.14", "getkirby/starterkit": "<=3.7.0.2", "gilacms/gila": "<=1.15.4", "gleez/cms": "<=1.3|==2", "globalpayments/php-sdk": "<2", "goalgorilla/open_social": "<12.3.11|>=12.4,<12.4.10|>=1*******-alpha1,<1*******-alpha11", "gogentooss/samlbase": "<1.2.7", "google/protobuf": "<3.15", "gos/web-socket-bundle": "<1.10.4|>=2,<2.6.1|>=3,<3.3", "gree/jose": "<2.2.1", "gregwar/rst": "<1.0.3", "grumpydictator/firefly-iii": "<6.1.17", "gugoan/economizzer": "<=0.9.0.0-beta1", "guzzlehttp/guzzle": "<6.5.8|>=7,<7.4.5", "guzzlehttp/oauth-subscriber": "<0.8.1", "guzzlehttp/psr7": "<1.9.1|>=2,<2.4.5", "haffner/jh_captcha": "<=2.1.3|>=3,<=3.0.2", "handcraftedinthealps/goodby-csv": "<1.4.3", "harvesthq/chosen": "<1.8.7", "helloxz/imgurl": "<=2.31", "hhxsv5/laravel-s": "<3.7.36", "hillelcoren/invoice-ninja": "<5.3.35", "himiklab/yii2-jqgrid-widget": "<1.0.8", "hjue/justwriting": "<=1", "hov/jobfair": "<1.0.13|>=2,<2.0.2", "httpsoft/http-message": "<1.0.12", "hyn/multi-tenant": ">=5.6,<5.7.2", "ibexa/admin-ui": ">=4.2,<4.2.3|>=4.6,<4.6.21", "ibexa/admin-ui-assets": ">=4.6.0.0-alpha1,<4.6.21", "ibexa/core": ">=4,<4.0.7|>=4.1,<4.1.4|>=4.2,<4.2.3|>=4.5,<4.5.6|>=4.6,<4.6.2", "ibexa/fieldtype-richtext": ">=4.6,<4.6.21", "ibexa/graphql": ">=2.5,<2.5.31|>=3.3,<3.3.28|>=4.2,<4.2.3", "ibexa/http-cache": ">=4.6,<4.6.14", "ibexa/post-install": "<1.0.16|>=4.6,<4.6.14", "ibexa/solr": ">=4.5,<4.5.4", "ibexa/user": ">=4,<4.4.3", "icecoder/icecoder": "<=8.1", "idno/known": "<=1.3.1", "ilicmiljan/secure-props": ">=1.2,<1.2.2", "illuminate/auth": "<5.5.10", "illuminate/cookie": ">=4,<=4.0.11|>=4.1,<6.18.31|>=7,<7.22.4", "illuminate/database": "<6.20.26|>=7,<7.30.5|>=8,<8.40", "illuminate/encryption": ">=4,<=4.0.11|>=4.1,<=4.1.31|>=4.2,<=4.2.22|>=5,<=5.0.35|>=5.1,<=5.1.46|>=5.2,<=5.2.45|>=5.3,<=5.3.31|>=5.4,<=5.4.36|>=5.5,<5.5.40|>=5.6,<5.6.15", "illuminate/view": "<6.20.42|>=7,<7.30.6|>=8,<8.75", "imdbphp/imdbphp": "<=5.1.1", "impresscms/impresscms": "<=1.4.5", "impresspages/impresspages": "<1.0.13", "in2code/femanager": "<6.4.2|>=7,<7.5.3|>=8,<8.3.1", "in2code/ipandlanguageredirect": "<5.1.2", "in2code/lux": "<17.6.1|>=18,<24.0.2", "in2code/powermail": "<7.5.1|>=8,<8.5.1|>=9,<10.9.1|>=11,<12.5.3|==13", "innologi/typo3-appointments": "<2.0.6", "intelliants/subrion": "<4.2.2", "inter-mediator/inter-mediator": "==5.5", "ipl/web": "<0.10.1", "islandora/crayfish": "<4.1", "islandora/islandora": ">=2,<2.4.1", "ivankristianto/phpwhois": "<=4.3", "jackalope/jackalope-doctrine-dbal": "<1.7.4", "jambagecom/div2007": "<0.10.2", "james-heinrich/getid3": "<1.9.21", "james-heinrich/phpthumb": "<=1.7.23", "jasig/phpcas": "<1.3.3", "jbartels/wec-map": "<3.0.3", "jcbrand/converse.js": "<3.3.3", "joelbutcher/socialstream": "<5.6|>=6,<6.2", "johnbillion/wp-crontrol": "<1.16.2", "joomla/application": "<1.0.13", "joomla/archive": "<1.1.12|>=2,<2.0.1", "joomla/database": ">=1,<2.2|>=3,<3.4", "joomla/filesystem": "<1.6.2|>=2,<2.0.1", "joomla/filter": "<1.4.4|>=2,<2.0.1", "joomla/framework": "<1.5.7|>=2.5.4,<=3.8.12", "joomla/input": ">=2,<2.0.2", "joomla/joomla-cms": "<3.9.12|>=4,<4.4.13|>=5,<5.2.6", "joomla/joomla-platform": "<1.5.4", "joomla/session": "<1.3.1", "joyqi/hyper-down": "<=2.4.27", "jsdecena/laracom": "<2.0.9", "jsmitty12/phpwhois": "<5.1", "juzaweb/cms": "<=3.4.2", "jweiland/events2": "<8.3.8|>=9,<9.0.6", "jweiland/kk-downloader": "<1.2.2", "kazist/phpwhois": "<=4.2.6", "kelvinmo/simplexrd": "<3.1.1", "kevinpapst/kimai2": "<1.16.7", "khodakhah/nodcms": "<=3", "kimai/kimai": "<=2.20.1", "kitodo/presentation": "<3.2.3|>=3.3,<3.3.4", "klaviyo/magento2-extension": ">=1,<3", "knplabs/knp-snappy": "<=1.4.2", "kohana/core": "<3.3.3", "koillection/koillection": "<1.6.12", "krayin/laravel-crm": "<=1.3", "kreait/firebase-php": ">=3.2,<3.8.1", "kumbiaphp/kumbiapp": "<=1.1.1", "la-haute-societe/tcpdf": "<6.2.22", "laminas/laminas-diactoros": "<2.18.1|==2.19|==2.20|==2.21|==2.22|==2.23|>=2.24,<2.24.2|>=2.25,<2.25.2", "laminas/laminas-form": "<2.17.1|>=3,<3.0.2|>=3.1,<3.1.1", "laminas/laminas-http": "<2.14.2", "lara-zeus/artemis": ">=1,<=1.0.6", "lara-zeus/dynamic-dashboard": ">=3,<=3.0.1", "laravel/fortify": "<1.11.1", "laravel/framework": "<10.48.29|>=11,<11.44.1|>=12,<12.1.1", "laravel/laravel": ">=5.4,<5.4.22", "laravel/pulse": "<1.3.1", "laravel/reverb": "<1.4", "laravel/socialite": ">=1,<2.0.10", "latte/latte": "<2.10.8", "lavalite/cms": "<=9|==10.1", "lcobucci/jwt": ">=3.4,<3.4.6|>=4,<4.0.4|>=4.1,<4.1.5", "league/commonmark": "<2.7", "league/flysystem": "<1.1.4|>=2,<2.1.1", "league/oauth2-server": ">=8.3.2,<8.4.2|>=8.5,<8.5.3", "leantime/leantime": "<3.3", "lexik/jwt-authentication-bundle": "<2.10.7|>=2.11,<2.11.3", "libreform/libreform": ">=2,<=2.0.8", "librenms/librenms": "<2017.08.18", "liftkit/database": "<2.13.2", "lightsaml/lightsaml": "<1.3.5", "limesurvey/limesurvey": "<6.5.12", "livehelperchat/livehelperchat": "<=3.91", "livewire/livewire": "<2.12.7|>=*******-beta1,<3.6.4", "livewire/volt": "<1.7", "lms/routes": "<2.1.1", "localizationteam/l10nmgr": "<7.4|>=8,<8.7|>=9,<9.2", "lomkit/laravel-rest-api": "<2.13", "luracast/restler": "<3.1", "luyadev/yii-helpers": "<1.2.1", "macropay-solutions/laravel-crud-wizard-free": "<3.4.17", "maestroerror/php-heic-to-jpg": "<1.0.5", "magento/community-edition": "<*******-patch13|==2.4.6|>=*******-patch1,<*******-patch11|>=*******-beta1,<*******-patch6|>=*******-beta1,<*******-patch1", "magento/core": "<=*******", "magento/magento1ce": "<*******-dev", "magento/magento1ee": ">=1,<********-dev", "magento/product-community-edition": "<*******-patch9|>=2.4.5,<*******-patch8|>=2.4.6,<*******-patch6|>=2.4.7,<*******-patch1", "magento/project-community-edition": "<=2.0.2", "magneto/core": "<*******-dev", "maikuolan/phpmussel": ">=1,<1.6", "mainwp/mainwp": "<=*******", "manogi/nova-tiptap": "<=3.2.6", "mantisbt/mantisbt": "<=2.26.3", "marcwillmann/turn": "<0.3.3", "marshmallow/nova-tiptap": "<5.7", "matomo/matomo": "<1.11", "matyhtf/framework": "<3.0.6", "mautic/core": "<5.2.6|>=*******-alpha,<6.0.2", "mautic/core-lib": ">=*******-beta,<4.4.13|>=*******-alpha,<5.1.1", "maximebf/debugbar": "<1.19", "mdanter/ecc": "<2", "mediawiki/abuse-filter": "<1.39.9|>=1.40,<1.41.3|>=1.42,<1.42.2", "mediawiki/cargo": "<3.6.1", "mediawiki/core": "<1.39.5|==1.40", "mediawiki/data-transfer": ">=1.39,<1.39.11|>=1.41,<1.41.3|>=1.42,<1.42.2", "mediawiki/matomo": "<2.4.3", "mediawiki/semantic-media-wiki": "<4.0.2", "mehrwert/phpmyadmin": "<3.2", "melisplatform/melis-asset-manager": "<5.0.1", "melisplatform/melis-cms": "<5.0.1", "melisplatform/melis-front": "<5.0.1", "mezzio/mezzio-swoole": "<3.7|>=4,<4.3", "mgallegos/laravel-jqgrid": "<=1.3", "microsoft/microsoft-graph": ">=1.16,<1.109.1|>=2,<2.0.1", "microsoft/microsoft-graph-beta": "<2.0.1", "microsoft/microsoft-graph-core": "<2.0.2", "microweber/microweber": "<=2.0.19", "mikehaertl/php-shellcommand": "<1.6.1", "miniorange/miniorange-saml": "<1.4.3", "mittwald/typo3_forum": "<1.2.1", "mobiledetect/mobiledetectlib": "<2.8.32", "modx/revolution": "<=3.1", "mojo42/jirafeau": "<4.4", "mongodb/mongodb": ">=1,<1.9.2", "monolog/monolog": ">=1.8,<1.12", "moodle/moodle": "<4.3.12|>=4.4,<4.4.8|>=4.5.0.0-beta,<4.5.4", "mos/cimage": "<0.7.19", "movim/moxl": ">=0.8,<=0.10", "movingbytes/social-network": "<=1.2.1", "mpdf/mpdf": "<=7.1.7", "munkireport/comment": "<4.1", "munkireport/managedinstalls": "<2.6", "munkireport/munki_facts": "<1.5", "munkireport/munkireport": ">=2.5.3,<5.6.3", "munkireport/reportdata": "<3.5", "munkireport/softwareupdate": "<1.6", "mustache/mustache": ">=2,<2.14.1", "mwdelaney/wp-enable-svg": "<=0.2", "namshi/jose": "<2.2", "nasirkhan/laravel-starter": "<11.11", "nategood/httpful": "<1", "neoan3-apps/template": "<1.1.1", "neorazorx/facturascripts": "<2022.04", "neos/flow": ">=1,<1.0.4|>=1.1,<1.1.1|>=2,<2.0.1|>=2.3,<2.3.16|>=3,<3.0.12|>=3.1,<3.1.10|>=3.2,<3.2.13|>=3.3,<3.3.13|>=4,<4.0.6", "neos/form": ">=1.2,<4.3.3|>=5,<5.0.9|>=5.1,<5.1.3", "neos/media-browser": "<7.3.19|>=8,<8.0.16|>=8.1,<8.1.11|>=8.2,<8.2.11|>=8.3,<8.3.9", "neos/neos": ">=1.1,<1.1.3|>=1.2,<1.2.13|>=2,<2.0.4|>=2.3,<3.0.20|>=3.1,<3.1.18|>=3.2,<3.2.14|>=3.3,<5.3.10|>=7,<7.0.9|>=7.1,<7.1.7|>=7.2,<7.2.6|>=7.3,<7.3.4|>=8,<8.0.2", "neos/swiftmailer": "<5.4.5", "nesbot/carbon": "<2.72.6|>=3,<3.8.4", "netcarver/textile": "<=4.1.2", "netgen/tagsbundle": ">=3.4,<3.4.11|>=4,<4.0.15", "nette/application": ">=2,<2.0.19|>=2.1,<2.1.13|>=2.2,<2.2.10|>=2.3,<2.3.14|>=2.4,<2.4.16|>=3,<3.0.6", "nette/nette": ">=2,<2.0.19|>=2.1,<2.1.13", "nilsteampassnet/teampass": "<*******-dev", "nitsan/ns-backup": "<13.0.1", "nonfiction/nterchange": "<4.1.1", "notrinos/notrinos-erp": "<=0.7", "noumo/easyii": "<=0.9", "novaksolutions/infusionsoft-php-sdk": "<1", "nukeviet/nukeviet": "<4.5.02", "nyholm/psr7": "<1.6.1", "nystudio107/craft-seomatic": "<3.4.12", "nzedb/nzedb": "<0.8", "nzo/url-encryptor-bundle": ">=4,<4.3.2|>=5,<5.0.1", "october/backend": "<1.1.2", "october/cms": "<1.0.469|==1.0.469|==1.0.471|==1.1.1", "october/october": "<3.7.5", "october/rain": "<1.0.472|>=1.1,<1.1.2", "october/system": "<3.7.5", "oliverklee/phpunit": "<3.5.15", "omeka/omeka-s": "<4.0.3", "onelogin/php-saml": "<2.10.4", "oneup/uploader-bundle": ">=1,<1.9.3|>=2,<2.1.5", "open-web-analytics/open-web-analytics": "<1.7.4", "opencart/opencart": ">=0", "openid/php-openid": "<2.3", "openmage/magento-lts": "<20.12.3", "opensolutions/vimbadmin": "<=3.0.15", "opensource-workshop/connect-cms": "<1.8.7|>=2,<2.4.7", "orchid/platform": ">=8,<14.43", "oro/calendar-bundle": ">=4.2,<=4.2.6|>=5,<=5.0.6|>=5.1,<5.1.1", "oro/commerce": ">=4.1,<5.0.11|>=5.1,<5.1.1", "oro/crm": ">=1.7,<1.7.4|>=3.1,<4.1.17|>=4.2,<4.2.7", "oro/crm-call-bundle": ">=4.2,<=4.2.5|>=5,<5.0.4|>=5.1,<5.1.1", "oro/customer-portal": ">=4.1,<=4.1.13|>=4.2,<=4.2.10|>=5,<=5.0.11|>=5.1,<=5.1.3", "oro/platform": ">=1.7,<1.7.4|>=3.1,<3.1.29|>=4.1,<4.1.17|>=4.2,<=4.2.10|>=5,<=5.0.12|>=5.1,<=5.1.3", "oveleon/contao-cookiebar": "<1.16.3|>=2,<2.1.3", "oxid-esales/oxideshop-ce": "<=7.0.5", "oxid-esales/paymorrow-module": ">=1,<1.0.2|>=2,<2.0.1", "packbackbooks/lti-1-3-php-library": "<5", "padraic/humbug_get_contents": "<1.1.2", "pagarme/pagarme-php": "<3", "pagekit/pagekit": "<=1.0.18", "paragonie/ecc": "<2.0.1", "paragonie/random_compat": "<2", "passbolt/passbolt_api": "<4.6.2", "paypal/adaptivepayments-sdk-php": "<=3.9.2", "paypal/invoice-sdk-php": "<=3.9", "paypal/merchant-sdk-php": "<3.12", "paypal/permissions-sdk-php": "<=3.9.1", "pear/archive_tar": "<1.4.14", "pear/auth": "<1.2.4", "pear/crypt_gpg": "<1.6.7", "pear/http_request2": "<2.7", "pear/pear": "<=1.10.1", "pegasus/google-for-jobs": "<1.5.1|>=2,<2.1.1", "personnummer/personnummer": "<3.0.2", "phanan/koel": "<5.1.4", "phenx/php-svg-lib": "<0.5.2", "php-censor/php-censor": "<2.0.13|>=2.1,<2.1.5", "php-mod/curl": "<2.3.2", "phpbb/phpbb": "<3.3.11", "phpems/phpems": ">=6,<=6.1.3", "phpfastcache/phpfastcache": "<6.1.5|>=7,<7.1.2|>=8,<8.0.7", "phpmailer/phpmailer": "<6.5", "phpmussel/phpmussel": ">=1,<1.6", "phpmyadmin/phpmyadmin": "<5.2.2", "phpmyfaq/phpmyfaq": "<3.2.5|==3.2.5|>=3.2.10,<=4.0.1", "phpoffice/common": "<0.2.9", "phpoffice/math": "<=0.2", "phpoffice/phpexcel": "<=1.8.2", "phpoffice/phpspreadsheet": "<1.29.9|>=2,<2.1.8|>=2.2,<2.3.7|>=3,<3.9", "phpseclib/phpseclib": "<2.0.47|>=3,<3.0.36", "phpservermon/phpservermon": "<3.6", "phpsysinfo/phpsysinfo": "<3.4.3", "phpunit/phpunit": ">=4.8.19,<4.8.28|>=5.0.10,<5.6.3", "phpwhois/phpwhois": "<=4.2.5", "phpxmlrpc/extras": "<0.6.1", "phpxmlrpc/phpxmlrpc": "<4.9.2", "pi/pi": "<=2.5", "pimcore/admin-ui-classic-bundle": "<1.7.6", "pimcore/customer-management-framework-bundle": "<4.2.1", "pimcore/data-hub": "<1.2.4", "pimcore/data-importer": "<1.8.9|>=1.9,<1.9.3", "pimcore/demo": "<10.3", "pimcore/ecommerce-framework-bundle": "<1.0.10", "pimcore/perspective-editor": "<1.5.1", "pimcore/pimcore": "<11.5.4", "piwik/piwik": "<1.11", "pixelfed/pixelfed": "<0.12.5", "plotly/plotly.js": "<2.25.2", "pocketmine/bedrock-protocol": "<8.0.2", "pocketmine/pocketmine-mp": "<5.25.2", "pocketmine/raklib": ">=0.14,<0.14.6|>=0.15,<0.15.1", "pressbooks/pressbooks": "<5.18", "prestashop/autoupgrade": ">=4,<4.10.1", "prestashop/blockreassurance": "<=5.1.3", "prestashop/blockwishlist": ">=2,<2.1.1", "prestashop/contactform": ">=1.0.1,<4.3", "prestashop/gamification": "<2.3.2", "prestashop/prestashop": "<8.1.6", "prestashop/productcomments": "<5.0.2", "prestashop/ps_contactinfo": "<=3.3.2", "prestashop/ps_emailsubscription": "<2.6.1", "prestashop/ps_facetedsearch": "<3.4.1", "prestashop/ps_linklist": "<3.1", "privatebin/privatebin": "<1.4|>=1.5,<1.7.4", "processwire/processwire": "<=3.0.229", "propel/propel": ">=*******-alpha1,<=*******-alpha7", "propel/propel1": ">=1,<=1.7.1", "pterodactyl/panel": "<=1.11.10", "ptheofan/yii2-statemachine": ">=*******-RC1-dev,<=2", "ptrofimov/beanstalk_console": "<1.7.14", "pubnub/pubnub": "<6.1", "punktde/pt_extbase": "<1.5.1", "pusher/pusher-php-server": "<2.2.1", "pwweb/laravel-core": "<=*******-beta", "pxlrbt/filament-excel": "<1.1.14|>=*******-alpha,<2.3.3", "pyrocms/pyrocms": "<=3.9.1", "qcubed/qcubed": "<=3.1.1", "quickapps/cms": "<=*******-beta2", "rainlab/blog-plugin": "<1.4.1", "rainlab/debugbar-plugin": "<3.1", "rainlab/user-plugin": "<=1.4.5", "rankmath/seo-by-rank-math": "<=1.0.95", "rap2hpoutre/laravel-log-viewer": "<0.13", "react/http": ">=0.7,<1.9", "really-simple-plugins/complianz-gdpr": "<6.4.2", "redaxo/source": "<5.18.3", "remdex/livehelperchat": "<4.29", "renolit/reint-downloadmanager": "<4.0.2|>=5,<5.0.1", "reportico-web/reportico": "<=8.1", "rhukster/dom-sanitizer": "<1.0.7", "rmccue/requests": ">=1.6,<1.8", "robrichards/xmlseclibs": ">=1,<3.0.4", "roots/soil": "<4.1", "roundcube/roundcubemail": "<1.5.10|>=1.6,<1.6.11", "rudloff/alltube": "<3.0.3", "rudloff/rtmpdump-bin": "<=2.3.1", "s-cart/core": "<6.9", "s-cart/s-cart": "<6.9", "sabberworm/php-css-parser": ">=1,<1.0.1|>=2,<2.0.1|>=3,<3.0.1|>=4,<4.0.1|>=5,<5.0.9|>=5.1,<5.1.3|>=5.2,<5.2.1|>=6,<6.0.2|>=7,<7.0.4|>=8,<8.0.1|>=8.1,<8.1.1|>=8.2,<8.2.1|>=8.3,<8.3.1", "sabre/dav": ">=1.6,<1.7.11|>=1.8,<1.8.9", "samwilson/unlinked-wikibase": "<1.42", "scheb/two-factor-bundle": "<3.26|>=4,<4.11", "sensiolabs/connect": "<4.2.3", "serluck/phpwhois": "<=4.2.6", "sfroemken/url_redirect": "<=1.2.1", "sheng/yiicms": "<1.2.1", "shopware/core": "<********-dev|>=6.6,<********-dev|>=*******-RC1-dev,<*******-RC2-dev", "shopware/platform": "<********-dev|>=6.6,<********-dev|>=*******-RC1-dev,<*******-RC2-dev", "shopware/production": "<=*******", "shopware/shopware": "<=5.7.17", "shopware/storefront": "<=*******|>=6.5.8,<*******-dev", "shopxo/shopxo": "<=6.4", "showdoc/showdoc": "<2.10.4", "shuchkin/simplexlsx": ">=1.0.12,<1.1.13", "silverstripe-australia/advancedreports": ">=1,<=2", "silverstripe/admin": "<1.13.19|>=2,<2.1.8", "silverstripe/assets": ">=1,<1.11.1", "silverstripe/cms": "<4.11.3", "silverstripe/comments": ">=1.3,<3.1.1", "silverstripe/forum": "<=0.6.1|>=0.7,<=0.7.3", "silverstripe/framework": "<5.3.23", "silverstripe/graphql": ">=2,<2.0.5|>=3,<3.8.2|>=4,<4.3.7|>=5,<5.1.3", "silverstripe/hybridsessions": ">=1,<2.4.1|>=2.5,<2.5.1", "silverstripe/recipe-cms": ">=4.5,<4.5.3", "silverstripe/registry": ">=2.1,<2.1.2|>=2.2,<2.2.1", "silverstripe/reports": "<5.2.3", "silverstripe/restfulserver": ">=1,<1.0.9|>=2,<2.0.4|>=2.1,<2.1.2", "silverstripe/silverstripe-omnipay": "<2.5.2|>=3,<3.0.2|>=3.1,<3.1.4|>=3.2,<3.2.1", "silverstripe/subsites": ">=2,<2.6.1", "silverstripe/taxonomy": ">=1.3,<1.3.1|>=2,<2.0.1", "silverstripe/userforms": "<3|>=5,<5.4.2", "silverstripe/versioned-admin": ">=1,<1.11.1", "simogeo/filemanager": "<=2.5", "simple-updates/phpwhois": "<=1", "simplesamlphp/saml2": "<=4.16.15|>=*******-alpha1,<=*******-alpha19", "simplesamlphp/saml2-legacy": "<=4.16.15", "simplesamlphp/simplesamlphp": "<1.18.6", "simplesamlphp/simplesamlphp-module-infocard": "<1.0.1", "simplesamlphp/simplesamlphp-module-openid": "<1", "simplesamlphp/simplesamlphp-module-openidprovider": "<0.9", "simplesamlphp/xml-common": "<1.20", "simplesamlphp/xml-security": "==1.6.11", "simplito/elliptic-php": "<1.0.6", "sitegeist/fluid-components": "<3.5", "sjbr/sr-feuser-register": "<2.6.2|>=5.1,<12.5", "sjbr/sr-freecap": "<2.4.6|>=2.5,<2.5.3", "sjbr/static-info-tables": "<2.3.1", "slim/psr7": "<1.4.1|>=1.5,<1.5.1|>=1.6,<1.6.1", "slim/slim": "<2.6", "slub/slub-events": "<3.0.3", "smarty/smarty": "<4.5.3|>=5,<5.1.1", "snipe/snipe-it": "<8.1", "socalnick/scn-social-auth": "<1.15.2", "socialiteproviders/steam": "<1.1", "spatie/browsershot": "<5.0.5", "spatie/image-optimizer": "<1.7.3", "spencer14420/sp-php-email-handler": "<1", "spipu/html2pdf": "<5.2.8", "spoon/library": "<1.4.1", "spoonity/tcpdf": "<6.2.22", "squizlabs/php_codesniffer": ">=1,<2.8.1|>=3,<3.0.1", "ssddanbrown/bookstack": "<24.05.1", "starcitizentools/citizen-skin": ">=1.9.4,<3.4", "starcitizentools/short-description": ">=4,<4.0.1", "starcitizentools/tabber-neue": ">=1.9.1,<2.7.2|>=3,<3.1.1", "statamic/cms": "<=5.16", "stormpath/sdk": "<9.9.99", "studio-42/elfinder": "<=2.1.64", "studiomitte/friendlycaptcha": "<0.1.4", "subhh/libconnect": "<7.0.8|>=8,<8.1", "sukohi/surpass": "<1", "sulu/form-bundle": ">=2,<2.5.3", "sulu/sulu": "<1.6.44|>=2,<2.5.25|>=2.6,<2.6.9|>=*******-alpha1,<*******-alpha3", "sumocoders/framework-user-bundle": "<1.4", "superbig/craft-audit": "<3.0.2", "svewap/a21glossary": "<=0.4.10", "swag/paypal": "<5.4.4", "swiftmailer/swiftmailer": "<6.2.5", "swiftyedit/swiftyedit": "<1.2", "sylius/admin-bundle": ">=1,<1.0.17|>=1.1,<1.1.9|>=1.2,<1.2.2", "sylius/grid": ">=1,<1.1.19|>=1.2,<1.2.18|>=1.3,<1.3.13|>=1.4,<1.4.5|>=1.5,<1.5.1", "sylius/grid-bundle": "<1.10.1", "sylius/paypal-plugin": "<1.6.2|>=1.7,<1.7.2|>=2,<2.0.2", "sylius/resource-bundle": ">=1,<1.3.14|>=1.4,<1.4.7|>=1.5,<1.5.2|>=1.6,<1.6.4", "sylius/sylius": "<1.12.19|>=********-alpha1,<1.13.4", "symbiote/silverstripe-multivaluefield": ">=3,<3.1", "symbiote/silverstripe-queuedjobs": ">=3,<3.0.2|>=3.1,<3.1.4|>=4,<4.0.7|>=4.1,<4.1.2|>=4.2,<4.2.4|>=4.3,<4.3.3|>=4.4,<4.4.3|>=4.5,<4.5.1|>=4.6,<4.6.4", "symbiote/silverstripe-seed": "<6.0.3", "symbiote/silverstripe-versionedfiles": "<=2.0.3", "symfont/process": ">=0", "symfony/cache": ">=3.1,<3.4.35|>=4,<4.2.12|>=4.3,<4.3.8", "symfony/dependency-injection": ">=2,<2.0.17|>=2.7,<2.7.51|>=2.8,<2.8.50|>=3,<3.4.26|>=4,<4.1.12|>=4.2,<4.2.7", "symfony/error-handler": ">=4.4,<4.4.4|>=5,<5.0.4", "symfony/form": ">=2.3,<2.3.35|>=2.4,<2.6.12|>=2.7,<2.7.50|>=2.8,<2.8.49|>=3,<3.4.20|>=4,<4.0.15|>=4.1,<4.1.9|>=4.2,<4.2.1", "symfony/framework-bundle": ">=2,<2.3.18|>=2.4,<2.4.8|>=2.5,<2.5.2|>=2.7,<2.7.51|>=2.8,<2.8.50|>=3,<3.4.26|>=4,<4.1.12|>=4.2,<4.2.7|>=5.3.14,<5.3.15|>=5.4.3,<5.4.4|>=6.0.3,<6.0.4", "symfony/http-client": ">=4.3,<5.4.47|>=6,<6.4.15|>=7,<7.1.8", "symfony/http-foundation": "<5.4.46|>=6,<6.4.14|>=7,<7.1.7", "symfony/http-kernel": ">=2,<4.4.50|>=5,<5.4.20|>=6,<6.0.20|>=6.1,<6.1.12|>=6.2,<6.2.6", "symfony/intl": ">=2.7,<2.7.38|>=2.8,<2.8.31|>=3,<3.2.14|>=3.3,<3.3.13", "symfony/maker-bundle": ">=1.27,<1.29.2|>=1.30,<1.31.1", "symfony/mime": ">=4.3,<4.3.8", "symfony/phpunit-bridge": ">=2.8,<2.8.50|>=3,<3.4.26|>=4,<4.1.12|>=4.2,<4.2.7", "symfony/polyfill": ">=1,<1.10", "symfony/polyfill-php55": ">=1,<1.10", "symfony/process": "<5.4.46|>=6,<6.4.14|>=7,<7.1.7", "symfony/proxy-manager-bridge": ">=2.7,<2.7.51|>=2.8,<2.8.50|>=3,<3.4.26|>=4,<4.1.12|>=4.2,<4.2.7", "symfony/routing": ">=2,<2.0.19", "symfony/runtime": ">=5.3,<5.4.46|>=6,<6.4.14|>=7,<7.1.7", "symfony/security": ">=2,<2.7.51|>=2.8,<3.4.49|>=4,<4.4.24|>=5,<5.2.8", "symfony/security-bundle": ">=2,<4.4.50|>=5,<5.4.20|>=6,<6.0.20|>=6.1,<6.1.12|>=6.2,<6.4.10|>=7,<7.0.10|>=7.1,<7.1.3", "symfony/security-core": ">=2.4,<2.6.13|>=2.7,<2.7.9|>=2.7.30,<2.7.32|>=2.8,<3.4.49|>=4,<4.4.24|>=5,<5.2.9", "symfony/security-csrf": ">=2.4,<2.7.48|>=2.8,<2.8.41|>=3,<3.3.17|>=3.4,<3.4.11|>=4,<4.0.11", "symfony/security-guard": ">=2.8,<3.4.48|>=4,<4.4.23|>=5,<5.2.8", "symfony/security-http": ">=2.3,<2.3.41|>=2.4,<2.7.51|>=2.8,<2.8.50|>=3,<3.4.26|>=4,<4.2.12|>=4.3,<4.3.8|>=4.4,<4.4.7|>=5,<5.0.7|>=5.1,<5.2.8|>=5.3,<5.4.47|>=6,<6.4.15|>=7,<7.1.8", "symfony/serializer": ">=2,<2.0.11|>=4.1,<4.4.35|>=5,<5.3.12", "symfony/symfony": "<5.4.47|>=6,<6.4.15|>=7,<7.1.8", "symfony/translation": ">=2,<2.0.17", "symfony/twig-bridge": ">=2,<4.4.51|>=5,<5.4.31|>=6,<6.3.8", "symfony/ux-autocomplete": "<2.11.2", "symfony/ux-live-component": "<2.25.1", "symfony/ux-twig-component": "<2.25.1", "symfony/validator": "<5.4.43|>=6,<6.4.11|>=7,<7.1.4", "symfony/var-exporter": ">=4.2,<4.2.12|>=4.3,<4.3.8", "symfony/web-profiler-bundle": ">=2,<2.3.19|>=2.4,<2.4.9|>=2.5,<2.5.4", "symfony/webhook": ">=6.3,<6.3.8", "symfony/yaml": ">=2,<2.0.22|>=2.1,<2.1.7|>=*******-beta1,<*******-beta2", "symphonycms/symphony-2": "<2.6.4", "t3/dce": "<0.11.5|>=2.2,<2.6.2", "t3g/svg-sanitizer": "<1.0.3", "t3s/content-consent": "<1.0.3|>=2,<2.0.2", "tastyigniter/tastyigniter": "<4", "tcg/voyager": "<=1.8", "tecnickcom/tc-lib-pdf-font": "<2.6.4", "tecnickcom/tcpdf": "<6.8", "terminal42/contao-tablelookupwizard": "<3.3.5", "thelia/backoffice-default-template": ">=2.1,<2.1.2", "thelia/thelia": ">=2.1,<2.1.3", "theonedemon/phpwhois": "<=4.2.5", "thinkcmf/thinkcmf": "<6.0.8", "thorsten/phpmyfaq": "<=4.0.1", "tikiwiki/tiki-manager": "<=17.1", "timber/timber": ">=0.16.6,<1.23.1|>=1.24,<1.24.1|>=2,<2.1", "tinymce/tinymce": "<7.2", "tinymighty/wiki-seo": "<1.2.2", "titon/framework": "<9.9.99", "tltneon/lgsl": "<7", "tobiasbg/tablepress": "<=*******-RC1", "topthink/framework": "<6.0.17|>=6.1,<=8.0.4", "topthink/think": "<=6.1.1", "topthink/thinkphp": "<=3.2.3|>=6.1.3,<=8.0.4", "torrentpier/torrentpier": "<=2.4.3", "tpwd/ke_search": "<4.0.3|>=4.1,<4.6.6|>=5,<5.0.2", "tribalsystems/zenario": "<=9.7.61188", "truckersmp/phpwhois": "<=4.3.1", "ttskch/pagination-service-provider": "<1", "twbs/bootstrap": "<=3.4.1|>=4,<=4.6.2", "twig/twig": "<3.11.2|>=3.12,<3.14.1|>=3.16,<3.19", "typo3/cms": "<9.5.29|>=10,<10.4.35|>=11,<11.5.23|>=12,<12.2", "typo3/cms-backend": "<4.1.14|>=4.2,<4.2.15|>=4.3,<4.3.7|>=4.4,<4.4.4|>=7,<=7.6.50|>=8,<=8.7.39|>=9,<=9.5.24|>=10,<10.4.46|>=11,<11.5.40|>=12,<=12.4.30|>=13,<=13.4.11", "typo3/cms-belog": ">=10,<=10.4.47|>=11,<=11.5.41|>=12,<=12.4.24|>=13,<=13.4.2", "typo3/cms-beuser": ">=10,<=10.4.47|>=11,<=11.5.41|>=12,<=12.4.24|>=13,<=13.4.2", "typo3/cms-core": "<=8.7.56|>=9,<=9.5.50|>=10,<=10.4.49|>=11,<=11.5.43|>=12,<=12.4.30|>=13,<=13.4.11", "typo3/cms-dashboard": ">=10,<=10.4.47|>=11,<=11.5.41|>=12,<=12.4.24|>=13,<=13.4.2", "typo3/cms-extbase": "<6.2.24|>=7,<7.6.8|==8.1.1", "typo3/cms-extensionmanager": ">=10,<=10.4.47|>=11,<=11.5.41|>=12,<=12.4.24|>=13,<=13.4.2", "typo3/cms-felogin": ">=4.2,<4.2.3", "typo3/cms-fluid": "<4.3.4|>=4.4,<4.4.1", "typo3/cms-form": ">=8,<=8.7.39|>=9,<=9.5.24|>=10,<=10.4.47|>=11,<=11.5.41|>=12,<=12.4.24|>=13,<=13.4.2", "typo3/cms-frontend": "<4.3.9|>=4.4,<4.4.5", "typo3/cms-indexed-search": ">=10,<=10.4.47|>=11,<=11.5.41|>=12,<=12.4.24|>=13,<=13.4.2", "typo3/cms-install": "<4.1.14|>=4.2,<4.2.16|>=4.3,<4.3.9|>=4.4,<4.4.5|>=12.2,<12.4.8|==13.4.2", "typo3/cms-lowlevel": ">=11,<=11.5.41", "typo3/cms-rte-ckeditor": ">=9.5,<9.5.42|>=10,<10.4.39|>=11,<11.5.30", "typo3/cms-scheduler": ">=11,<=11.5.41", "typo3/cms-setup": ">=9,<=9.5.50|>=10,<=10.4.49|>=11,<=11.5.43|>=12,<=12.4.30|>=13,<=13.4.11", "typo3/cms-webhooks": ">=12,<=12.4.30|>=13,<=13.4.11", "typo3/flow": ">=1,<1.0.4|>=1.1,<1.1.1|>=2,<2.0.1|>=2.3,<2.3.16|>=3,<3.0.12|>=3.1,<3.1.10|>=3.2,<3.2.13|>=3.3,<3.3.13|>=4,<4.0.6", "typo3/html-sanitizer": ">=1,<=1.5.2|>=2,<=2.1.3", "typo3/neos": ">=1.1,<1.1.3|>=1.2,<1.2.13|>=2,<2.0.4|>=2.3,<2.3.99|>=3,<3.0.20|>=3.1,<3.1.18|>=3.2,<3.2.14|>=3.3,<3.3.23|>=4,<4.0.17|>=4.1,<4.1.16|>=4.2,<4.2.12|>=4.3,<4.3.3", "typo3/phar-stream-wrapper": ">=1,<2.1.1|>=3,<3.1.1", "typo3/swiftmailer": ">=4.1,<4.1.99|>=5.4,<5.4.5", "typo3fluid/fluid": ">=2,<2.0.8|>=2.1,<2.1.7|>=2.2,<2.2.4|>=2.3,<2.3.7|>=2.4,<2.4.4|>=2.5,<2.5.11|>=2.6,<2.6.10", "ua-parser/uap-php": "<3.8", "uasoft-indonesia/badaso": "<=2.9.7", "unisharp/laravel-filemanager": "<2.9.1", "universal-omega/dynamic-page-list3": "<3.6.4", "unopim/unopim": "<0.1.5", "userfrosting/userfrosting": ">=0.3.1,<4.6.3", "usmanhalalit/pixie": "<1.0.3|>=2,<2.0.2", "uvdesk/community-skeleton": "<=1.1.1", "uvdesk/core-framework": "<=1.1.1", "vanilla/safecurl": "<0.9.2", "verbb/comments": "<1.5.5", "verbb/formie": "<=2.1.43", "verbb/image-resizer": "<2.0.9", "verbb/knock-knock": "<1.2.8", "verot/class.upload.php": "<=2.1.6", "vertexvaar/falsftp": "<0.2.6", "villagedefrance/opencart-overclocked": "<=1.11.1", "vova07/yii2-fileapi-widget": "<0.1.9", "vrana/adminer": "<4.8.1", "vufind/vufind": ">=2,<9.1.1", "waldhacker/hcaptcha": "<2.1.2", "wallabag/tcpdf": "<6.2.22", "wallabag/wallabag": "<2.6.11", "wanglelecc/laracms": "<=1.0.3", "wapplersystems/a21glossary": "<=0.4.10", "web-auth/webauthn-framework": ">=3.3,<3.3.4|>=4.5,<4.9", "web-auth/webauthn-lib": ">=4.5,<4.9", "web-feet/coastercms": "==5.5", "web-tp3/wec_map": "<3.0.3", "webbuilders-group/silverstripe-kapost-bridge": "<0.4", "webcoast/deferred-image-processing": "<1.0.2", "webklex/laravel-imap": "<5.3", "webklex/php-imap": "<5.3", "webpa/webpa": "<3.1.2", "wikibase/wikibase": "<=1.39.3", "wikimedia/parsoid": "<0.12.2", "willdurand/js-translation-bundle": "<2.1.1", "winter/wn-backend-module": "<1.2.4", "winter/wn-cms-module": "<1.0.476|>=1.1,<1.1.11|>=1.2,<1.2.7", "winter/wn-dusk-plugin": "<2.1", "winter/wn-system-module": "<1.2.4", "wintercms/winter": "<=1.2.3", "wireui/wireui": "<1.19.3|>=2,<2.1.3", "woocommerce/woocommerce": "<6.6|>=8.8,<8.8.5|>=8.9,<8.9.3", "wp-cli/wp-cli": ">=0.12,<2.5", "wp-graphql/wp-graphql": "<=1.14.5", "wp-premium/gravityforms": "<2.4.21", "wpanel/wpanel4-cms": "<=4.3.1", "wpcloud/wp-stateless": "<3.2", "wpglobus/wpglobus": "<=1.9.6", "wwbn/avideo": "<14.3", "xataface/xataface": "<3", "xpressengine/xpressengine": "<3.0.15", "yab/quarx": "<2.4.5", "yeswiki/yeswiki": "<4.5.4", "yetiforce/yetiforce-crm": "<6.5", "yidashi/yii2cmf": "<=2", "yii2mod/yii2-cms": "<1.9.2", "yiisoft/yii": "<1.1.31", "yiisoft/yii2": "<2.0.52", "yiisoft/yii2-authclient": "<2.2.15", "yiisoft/yii2-bootstrap": "<2.0.4", "yiisoft/yii2-dev": "<=2.0.45", "yiisoft/yii2-elasticsearch": "<2.0.5", "yiisoft/yii2-gii": "<=2.2.4", "yiisoft/yii2-jui": "<2.0.4", "yiisoft/yii2-redis": "<2.0.20", "yikesinc/yikes-inc-easy-mailchimp-extender": "<6.8.6", "yoast-seo-for-typo3/yoast_seo": "<7.2.3", "yourls/yourls": "<=1.8.2", "yuan1994/tpadmin": "<=1.3.12", "zencart/zencart": "<=1.5.7.0-beta", "zendesk/zendesk_api_client_php": "<2.2.11", "zendframework/zend-cache": ">=2.4,<2.4.8|>=2.5,<2.5.3", "zendframework/zend-captcha": ">=2,<2.4.9|>=2.5,<2.5.2", "zendframework/zend-crypt": ">=2,<2.4.9|>=2.5,<2.5.2", "zendframework/zend-db": "<2.2.10|>=2.3,<2.3.5", "zendframework/zend-developer-tools": ">=1.2.2,<1.2.3", "zendframework/zend-diactoros": "<1.8.4", "zendframework/zend-feed": "<2.10.3", "zendframework/zend-form": ">=2,<2.2.7|>=2.3,<2.3.1", "zendframework/zend-http": "<2.8.1", "zendframework/zend-json": ">=2.1,<2.1.6|>=2.2,<2.2.6", "zendframework/zend-ldap": ">=2,<2.0.99|>=2.1,<2.1.99|>=2.2,<2.2.8|>=2.3,<2.3.3", "zendframework/zend-mail": "<2.4.11|>=2.5,<2.7.2", "zendframework/zend-navigation": ">=2,<2.2.7|>=2.3,<2.3.1", "zendframework/zend-session": ">=2,<2.2.9|>=2.3,<2.3.4", "zendframework/zend-validator": ">=2.3,<2.3.6", "zendframework/zend-view": ">=2,<2.2.7|>=2.3,<2.3.1", "zendframework/zend-xmlrpc": ">=2.1,<2.1.6|>=2.2,<2.2.6", "zendframework/zendframework": "<=3", "zendframework/zendframework1": "<1.12.20", "zendframework/zendopenid": "<2.0.2", "zendframework/zendrest": "<2.0.2", "zendframework/zendservice-amazon": "<2.0.3", "zendframework/zendservice-api": "<1", "zendframework/zendservice-audioscrobbler": "<2.0.2", "zendframework/zendservice-nirvanix": "<2.0.2", "zendframework/zendservice-slideshare": "<2.0.2", "zendframework/zendservice-technorati": "<2.0.2", "zendframework/zendservice-windowsazure": "<2.0.2", "zendframework/zendxml": ">=1,<1.0.1", "zenstruck/collection": "<0.2.1", "zetacomponents/mail": "<1.8.2", "zf-commons/zfc-user": "<1.2.2", "zfcampus/zf-apigility-doctrine": ">=1,<1.0.3", "zfr/zfr-oauth2-server-module": "<0.1.2", "zoujingli/thinkadmin": "<=6.1.53"}, "default-branch": true, "type": "metapackage", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "maintainer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "maintainer"}], "description": "Prevents installation of composer packages with known security vulnerabilities: no API, simply require it", "keywords": ["dev"], "support": {"issues": "https://github.com/Roave/SecurityAdvisories/issues", "source": "https://github.com/Roave/SecurityAdvisories/tree/latest"}, "funding": [{"url": "https://github.com/Ocramius", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/roave/security-advisories", "type": "tidelift"}], "time": "2025-07-22T16:07:09+00:00"}, {"name": "symfony/var-dumper", "version": "v5.4.48", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "42f18f170aa86d612c3559cfb3bd11a375df32c8"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/symfony/var-dumper/v5.4.48/symfony-var-dumper-v5.4.48.zip", "reference": "42f18f170aa86d612c3559cfb3bd11a375df32c8", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/console": "<4.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^4.4|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/uid": "^5.1|^6.0", "twig/twig": "^2.13|^3.0.4"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-08T15:21:10+00:00"}, {"name": "xlerr/yii2-gii-templates", "version": "2.0.2", "source": {"type": "git", "url": "************************:xlerr/yii2-gii-templates.git", "reference": "c519c45132b8729a2952fad1a53640c07efc3e6e"}, "dist": {"type": "zip", "url": "https://biz-satis-test.kuainiujinke.com/composer/dist/xlerr/yii2-gii-templates/xlerr-yii2-gii-templates-2.0.2-e5da3d.zip", "reference": "c519c45132b8729a2952fad1a53640c07efc3e6e"}, "require": {"php": ">=5.6", "xlerr/yii2-common-widgets": "^1.1", "yiisoft/yii2": "~2.0.0"}, "require-dev": {"yiisoft/yii2-debug": "~2.0.0", "yiisoft/yii2-gii": "~2.0.0"}, "type": "library", "license": ["MIT"], "authors": [{"name": "xlerr", "email": "<EMAIL>"}], "time": "2021-06-01T09:37:38+00:00"}, {"name": "yiisoft/yii2-debug", "version": "2.1.27", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-debug.git", "reference": "44e158914911ef81cd7111fd6d46b918f65fae7c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-debug/zipball/44e158914911ef81cd7111fd6d46b918f65fae7c", "reference": "44e158914911ef81cd7111fd6d46b918f65fae7c", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.4", "yiisoft/yii2": "~2.0.13"}, "require-dev": {"cweagans/composer-patches": "^1.7", "phpunit/phpunit": "4.8.34", "yiisoft/yii2-coding-standards": "~2.0", "yiisoft/yii2-swiftmailer": "*"}, "type": "yii2-extension", "extra": {"patches": {"phpunit/phpunit": {"Fix PHP 7 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php7.patch", "Fix PHP 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php8.patch", "Fix PHP 8.1 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php81.patch"}, "phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_mock_objects.patch"}}, "branch-alias": {"dev-master": "2.0.x-dev"}, "composer-exit-on-patch-failure": true}, "autoload": {"psr-4": {"yii\\debug\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The debugger extension for the Yii framework", "keywords": ["debug", "debugger", "dev", "yii2"], "support": {"forum": "https://www.yiiframework.com/forum/", "irc": "ircs://irc.libera.chat:6697/yii", "issues": "https://github.com/yiisoft/yii2-debug/issues", "source": "https://github.com/yiisoft/yii2-debug", "wiki": "https://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-debug", "type": "tidelift"}], "time": "2025-06-08T13:32:11+00:00"}, {"name": "yiisoft/yii2-faker", "version": "2.0.5", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-faker.git", "reference": "8c361657143bfaea58ff7dcc9bf51f1991a46f5d"}, "dist": {"type": "zip", "url": "https://mirrors.huaweicloud.com/repository/php/yiisoft/yii2-faker/2.0.5/yiisoft-yii2-faker-2.0.5.zip", "reference": "8c361657143bfaea58ff7dcc9bf51f1991a46f5d", "shasum": ""}, "require": {"fakerphp/faker": "~1.9|~1.10", "yiisoft/yii2": "~2.0.0"}, "require-dev": {"cweagans/composer-patches": "^1.7", "phpunit/phpunit": "4.8.34"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}, "composer-exit-on-patch-failure": true, "patches": {"phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_mock_objects.patch"}, "phpunit/phpunit": {"Fix PHP 7 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php7.patch", "Fix PHP 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php8.patch"}}}, "autoload": {"psr-4": {"yii\\faker\\": "src"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Fixture generator. The Faker integration for the Yii framework.", "keywords": ["Fixture", "faker", "yii2"], "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-faker", "type": "tidelift"}], "time": "2020-11-10T12:27:35+00:00"}, {"name": "yiisoft/yii2-gii", "version": "2.2.7", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-gii.git", "reference": "f17c7ef7ef3081213f612b37ad0ceaa8e8cd3d3b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-gii/zipball/f17c7ef7ef3081213f612b37ad0ceaa8e8cd3d3b", "reference": "f17c7ef7ef3081213f612b37ad0ceaa8e8cd3d3b", "shasum": ""}, "require": {"phpspec/php-diff": "^1.1.0", "yiisoft/yii2": "~2.0.46"}, "require-dev": {"cweagans/composer-patches": "^1.7", "phpunit/phpunit": "4.8.34", "yiisoft/yii2-coding-standards": "~2.0"}, "type": "yii2-extension", "extra": {"patches": {"phpunit/phpunit": {"Fix PHP 7 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php7.patch", "Fix PHP 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php8.patch", "Fix PHP 8.1 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php81.patch"}, "phpunit/php-file-iterator": {"Fix PHP 8.1 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_path_file_iterator.patch"}, "phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_mock_objects.patch"}}, "branch-alias": {"dev-master": "2.0.x-dev"}, "composer-exit-on-patch-failure": true}, "autoload": {"psr-4": {"yii\\gii\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The Gii extension for the Yii framework", "keywords": ["code generator", "dev", "gii", "yii2"], "support": {"forum": "https://www.yiiframework.com/forum/", "irc": "ircs://irc.libera.chat:6697/yii", "issues": "https://github.com/yiisoft/yii2-gii/issues", "source": "https://github.com/yiisoft/yii2-gii", "wiki": "https://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-gii", "type": "tidelift"}], "time": "2025-02-13T21:21:17+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {"roave/security-advisories": 20}, "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=7.4", "ext-bcmath": "*", "ext-curl": "*", "ext-intl": "*", "ext-json": "*", "ext-pdo": "*", "ext-zlib": "*"}, "platform-dev": {}, "plugin-api-version": "2.6.0"}