#pod 上新国家需注意时区（一般情况下时区不用改，取的k8s变量里设置的时区）、启动参数指定新国家、镜像等参数
apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: biz
  name: biz-prod
  labels:
    app: biz-prod
    name: biz-prod
spec:
  replicas: 1
  selector:
    matchLabels:
      app: biz-prod
  template:
    metadata:
      labels:
        app: biz-prod
    spec:
      imagePullSecrets:
        - name: aly-hongkong-registry
      containers:
        - env:
            - name: TZ
              valueFrom:
                configMapKeyRef:
                  name: biz-cm
                  key: user.timezone
          name: biz-prod
          image: registry.cn-hongkong.aliyuncs.com/dx-biz/global-portal:prod-tha-0469a32
          imagePullPolicy: Always
          lifecycle:
            postStart:
              exec:
                command:
                  - /bin/sh
                  - '-c'
                  - >-
                    /data/www/wwwroot/portal/init --env=prod-ind --overwrite=y
                    && /data/www/wwwroot/portal/release biz-portal-ind
            preStop:
              exec:
                command:
                  - supervisorctl
                  - stop
                  - xxl-job
                  - '&&'
                  - sleep
                  - '10'
          ports:
            - containerPort: 80
              protocol: TCP
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 15
            successThreshold: 1
            tcpSocket:
              port: 80
            timeoutSeconds: 5
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 15
            successThreshold: 1
            tcpSocket:
              port: 80
            timeoutSeconds: 5
          resources:
            requests:
              cpu: 100m
              memory: 512Mi
            limits:
              cpu: '1'
              memory: 512Mi
---
#svc 无需调整
apiVersion: v1
kind: Service
metadata:
  name: biz-prod-svc
  namespace: biz
spec:
  ports:
    - name: http
      port: 80
      protocol: TCP
      targetPort: 80
  selector:
    app: biz-prod
  type: ClusterIP

---
#ingress 上新国家需注意域名
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-dx
    kubernetes.io/tls-acme: 'true'
    nginx.ingress.kubernetes.io/ssl-redirect: 'true'
  name: biz.yblossom.com
  namespace: biz
spec:
  rules:
    - host: biz.yblossom.com
      http:
        paths:
          - backend:
              serviceName: biz-prod-svc
              servicePort: 80
            path: /
            pathType: ImplementationSpecific
  tls:
    - hosts:
        - biz.yblossom.com
      secretName: biz.yblossom.com-tls

