# registry.cn-hongkong.aliyuncs.com/dx-biz/portal.base:20210827.2
# FROM registry.cn-hongkong.aliyuncs.com/dx-biz/centos7_php:20200429
#
# RUN yum -y install https://rpms.remirepo.net/enterprise/remi-release-7.rpm \
#     && yum -y install yum-utils \
#     && yum-config-manager --enable remi-php74 \
#     && yum -y remove php* \
#     && yum install -y php php-cli php-fpm php-curl php-mysqli php-zip php-xml php-gd php-mbstring php-bcmath php-opcache php-pecl-zip php-intl \
#     && yum clean all \
#     && curl https://mirrors.aliyun.com/composer/composer.phar -o /usr/local/bin/composer \
#     && chmod 755 /usr/local/bin/composer \
#     && mkdir -p /data/logs/nginx \
#     && mkdir -p /data/nginx/conf \
#     && mkdir -p /data/logs/php7/php/ \
#     && chown -R nginx.nginx /data

# FROM registry.cn-hongkong.aliyuncs.com/dx-biz/portal.base:20210827.2
# FROM overseas-registry.ap-southeast-1.cr.aliyuncs.com/biz/global-portal.base:7.4.27
FROM overseas-registry.ap-southeast-1.cr.aliyuncs.com/biz/global-portal.base:7.4.33

COPY . /opt

RUN composer config -g repo.packagist composer https://mirrors.aliyun.com/composer/ \
 && mkdir -p /data/www/wwwroot/portal/ \
 && mv /opt/docker/nginx/* /data/nginx/conf \
 && mv /opt/docker/php/php.ini /etc/php.ini \
 && mv /opt/docker/php/php-fpm.conf /etc/php-fpm.conf \
 && mv /opt/docker/supervisor/supervisord.conf /etc/supervisord.conf \
 && mv /opt/* /data/www/wwwroot/portal \
 && chown -R nginx.nginx /data/www/wwwroot/portal

EXPOSE 80