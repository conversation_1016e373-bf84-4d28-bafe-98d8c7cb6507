{"payment_fee": {"sql": "SELECT * FROM(SELECT DISTINCT channel_name, fee_type AS type, data_month, merchant_subject AS merchant, subject, initAmount, lastAmount, initAmount - lastAmount AS diff, lastReportDate, CASE WHEN initAmount != 0 && lastAmount = 0 THEN 100.00 WHEN initAmount = 0 && lastAmount != 0 THEN 100.00 WHEN lastAmount != 0 THEN ROUND((initAmount - lastAmount) / lastAmount * 100, 2) ELSE 0.00 END AS differenceRate, id FROM (SELECT initRecords.channel_name AS channel_name, initRecords.type AS fee_type, initRecords.data_month AS data_month, initRecords.subject AS subject, initRecords.merchant_subject AS merchant_subject, IFNULL(initRecords.amount, 0) AS initAmount, initRecords.id AS id, IFNULL(last1.amount, 0) AS lastAmount, last1.report_date AS lastReportDate FROM (SELECT * FROM data_report_finance_payment_fee WHERE batch_number = :batchNumber AND status = 'init') AS initRecords LEFT JOIN (SELECT * FROM (SELECT * FROM data_report_finance_payment_fee WHERE status = 'completed' AND create_at >= :createAt GROUP BY subject, type, merchant_subject, channel_name, data_month, create_at ORDER BY create_at desc) a GROUP BY subject, type, merchant_subject, channel_name, data_month) AS last1 ON last1.channel_name = initRecords.channel_name AND last1.type = initRecords.type AND last1.data_month = initRecords.data_month AND last1.subject = initRecords.subject AND last1.merchant_subject = initRecords.merchant_subject UNION ALL SELECT last1 . channel_name AS channel_name , last1 . type AS fee_type , last1 . data_month AS data_month , last1 . subject AS subject , last1 . merchant_subject AS merchant_subject , IFNULL ( initRecords . amount , 0) AS initAmount , initRecords . id AS id , IFNULL ( last1 . amount , 0) AS lastAmount , last1 . report_date AS lastReportDate FROM (SELECT * FROM (SELECT * FROM data_report_finance_payment_fee WHERE status = 'completed' AND create_at >= :createAt GROUP BY subject, type, merchant_subject, channel_name, data_month, create_at ORDER BY create_at desc) a GROUP BY subject, type, merchant_subject, channel_name, data_month) AS last1 LEFT JOIN (SELECT * FROM data_report_finance_payment_fee WHERE batch_number = :batchNumber AND status = 'init') AS initRecords ON last1.channel_name = initRecords.channel_name AND last1.type = initRecords.type AND last1.data_month = initRecords.data_month AND last1.merchant_subject = initRecords.merchant_subject AND last1.subject = initRecords.subject) AS tmp WHERE data_month >= :month) a WHERE 1 = 1;", "params": {":month": "12 months ago", ":createAt": "2024-08-01"}, "threshold": 0}, "income_statement": {"sql": "select *\nfrom (SELECT DISTINCT loan_channel,\n                      fee_type                as type,\n                      cost_type,\n                      data_month,\n                      initAmount,\n                      lastAmount,\n                      initAmount - lastAmount as diff,\n                      lastReportDate,\n                      CASE\n                          WHEN initAmount != 0 && lastAmount = 0 THEN 100.00\n                          WHEN initAmount = 0 && lastAmount != 0 THEN 100.00\n                          WHEN lastAmount != 0 THEN ROUND((initAmount - lastAmount) / lastAmount * 100, 2)\n                          ELSE 0.00 END       AS differenceRate,\n                      id\n      FROM (SELECT initRecords.loan_channel      loan_channel,\n                   initRecords.type              fee_type,\n                   initRecords.cost_type         cost_type,\n                   initRecords.data_month        data_month,\n                   IFNULL(initRecords.amount, 0) initAmount,\n                   initRecords.id                id,\n                   IFNULL(last1.amount, 0)       lastAmount,\n                   last1.report_date             lastReportDate\n            FROM (SELECT *\n                  FROM data_report_finance_income_statement\n                  WHERE batch_number = :batchNumber\n                    AND status = 'init'\n                    AND to_system = 'Finance') AS initRecords\n                     LEFT JOIN (select *\n                                from (SELECT *\n                                      FROM data_report_finance_income_statement\n                                      WHERE status = 'completed'\n                                        AND to_system = 'Finance'\n                                        AND create_at >= :createAt\n                                      group by loan_channel, type, data_month, cost_type, create_at\n                                      order by create_at desc) a\n                                group by loan_channel, type, cost_type,data_month) AS last1\n                               ON last1.loan_channel = initRecords.loan_channel AND last1.type = initRecords.type AND\n                                  last1.data_month = initRecords.data_month AND last1.cost_type = initRecords.cost_type\n            UNION ALL\n            SELECT last1.loan_channel            loan_channel,\n                   last1.type                    fee_type,\n                   last1.cost_type               cost_type,\n                   last1.data_month              data_month,\n                   IFNULL(initRecords.amount, 0) initAmount,\n                   initRecords.id                id,\n                   IFNULL(last1.amount, 0)       lastAmount,\n                   last1.report_date             lastReportDate\n            FROM (select *\n                  from (SELECT *\n                        FROM data_report_finance_income_statement\n                        WHERE status = 'completed'\n                          AND to_system = 'Finance'\n                          AND create_at >= :createAt\n                        group by loan_channel, type, data_month, cost_type, create_at\n                        order by create_at desc) a\n                  group by loan_channel, type,cost_type, data_month) AS last1\n                     LEFT JOIN (SELECT *\n                                FROM data_report_finance_income_statement\n                                WHERE batch_number = :batchNumber\n                                  AND status = 'init'\n                                  AND to_system = 'Finance') AS initRecords\n                               ON last1.loan_channel = initRecords.loan_channel AND last1.type = initRecords.type AND\n                                  last1.data_month = initRecords.data_month AND\n                                  last1.cost_type = initRecords.cost_type) AS tmp\n      WHERE data_month >= :month) a\nwhere 1 = 1;", "params": {":month": "12 months ago", ":createAt": "2024-08-01"}, "threshold": 0}, "finance_general_cost": {"sql": "select *\nfrom (SELECT DISTINCT loan_channel,\n                      fee_type                as type,\n                      cost_type,\n                      data_month,\n                      initAmount,\n                      lastAmount,\n                      initAmount - lastAmount as diff,\n                      lastReportDate,\n                      CASE\n                          WHEN initAmount != 0 && lastAmount = 0 THEN 100.00\n                          WHEN initAmount = 0 && lastAmount != 0 THEN 100.00\n                          WHEN lastAmount != 0 THEN ROUND((initAmount - lastAmount) / lastAmount * 100, 2)\n                          ELSE 0.00 END       AS differenceRate,\n                      id\n      FROM (SELECT initRecords.loan_channel      loan_channel,\n                   initRecords.type              fee_type,\n                   initRecords.cost_type         cost_type,\n                   initRecords.data_month        data_month,\n                   IFNULL(initRecords.amount, 0) initAmount,\n                   initRecords.id                id,\n                   IFNULL(last1.amount, 0)       lastAmount,\n                   last1.report_date             lastReportDate\n            FROM (SELECT *\n                  FROM data_report_finance_income_statement\n                  WHERE batch_number = :batchNumber\n                    AND status = 'init'\n                    AND to_system = 'Finance_general_cost') AS initRecords\n                     LEFT JOIN (select *\n                                from (SELECT *\n                                      FROM data_report_finance_income_statement\n                                      WHERE status = 'completed'\n                                        AND to_system = 'Finance_general_cost'\n                                        AND create_at >= :createAt\n                                      group by loan_channel, type, data_month, cost_type, create_at\n                                      order by create_at desc) a\n                                group by loan_channel, type, cost_type,data_month) AS last1\n                               ON last1.loan_channel = initRecords.loan_channel AND last1.type = initRecords.type AND\n                                  last1.data_month = initRecords.data_month AND last1.cost_type = initRecords.cost_type\n            UNION ALL\n            SELECT last1.loan_channel            loan_channel,\n                   last1.type                    fee_type,\n                   last1.cost_type               cost_type,\n                   last1.data_month              data_month,\n                   IFNULL(initRecords.amount, 0) initAmount,\n                   initRecords.id                id,\n                   IFNULL(last1.amount, 0)       lastAmount,\n                   last1.report_date             lastReportDate\n            FROM (select *\n                  from (SELECT *\n                        FROM data_report_finance_income_statement\n                        WHERE status = 'completed'\n                          AND to_system = 'Finance_general_cost'\n                          AND create_at >= :createAt\n                        group by loan_channel, type, data_month, cost_type, create_at\n                        order by create_at desc) a\n                  group by loan_channel, type,cost_type, data_month) AS last1\n                     LEFT JOIN (SELECT *\n                                FROM data_report_finance_income_statement\n                                WHERE batch_number = :batchNumber\n                                  AND status = 'init'\n                                  AND to_system = 'Finance_general_cost') AS initRecords\n                               ON last1.loan_channel = initRecords.loan_channel AND last1.type = initRecords.type AND\n                                  last1.data_month = initRecords.data_month AND\n                                  last1.cost_type = initRecords.cost_type) AS tmp\n      WHERE data_month >= :month) a\nwhere 1 = 1;", "params": {":month": "12 months ago", ":createAt": "2024-08-01"}, "threshold": 0}}