<?php

namespace payment\models\paystats;

use Carbon\Carbon;
use payment\models\ReportPaymentChannelStats;
use payment\traits\RPCSChartTrait;
use yii\db\Expression;

class SummarizeByMonthSearch extends ReportPaymentChannelStats
{
    use RPCSChartTrait;

    public $startDate;
    public $endDate;

    public const LOAN_TYPE = [
        'import_success_counts',
        'import_counts',
        'pay_success_amount',
        'pay_fee_amount'
    ];

    public const REPAY_TYPE = [
        'repay_success_counts_day',
        'repay_counts_day',
        'repay_success_amount',
        'repay_fee_amount'
    ];

    public function attributeLabels(): array
    {
        return [
            'startDate' => '开始日期',
            'endDate' => '结束日期'
        ];
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            [['startDate'], 'default', 'value' => Carbon::now()->subMonths()->format('Y-m')],
            [['endDate'], 'default', 'value' => Carbon::now()->format('Y-m')],
            [['startDate', 'endDate'], 'safe'],
        ];
    }


    /**
     * @param array $params
     * @return array
     */
    public function search(array $params): array
    {
        $this->load($params);
        if (!$this->validate()) {
            return [];
        }

        if (Carbon::parse($this->startDate)->diffInMonths(Carbon::parse($this->endDate)) > 12) {
            $this->addError('endDate', '时间维度不能超过12个月!');
        }

        $selects = ['month' => new Expression("DATE_FORMAT(`date`, '%Y-%m')")];

        $types = [
            'import_success_counts',
            'repay_success_counts_day',
            'import_counts',
            'repay_counts_day',
            'pay_success_amount',
            'repay_success_amount',
            'pay_fee_amount',
            'pay_success_amount',
            'repay_fee_amount',
            'repay_success_amount'
        ];

        foreach ($types as $type) {
            $selects[$type] = new Expression(sprintf('SUM(IF(`type`=\'%s\', `values`, 0)) ', $type));
        }

        $query = self::find()->select($selects)
            ->where(['type' => $types])
            ->andWhere(['>=', 'date', Carbon::parse($this->startDate)->toDateString()])
            ->andWhere(['<', 'date', Carbon::parse($this->endDate)->endOfMonth()->addDay()->toDateString()])
            ->groupBy(['month'])->orderBy(['month' => SORT_ASC]);

        $data = $query->asArray()->all();

        $months = array_column($data, 'month');

        $loanMonth = [];
        $repayMonth = [];
        $leftChart = [
            'month' => $months,
            'loan_success_rate' => [],
            'repay_success_rate' => []
        ];
        $rightChart = [
            'month' => $months,
            'thousand_repay_cost' => [],
            'thousand_loan_cost' => []
        ];
        $leftChart['import_success_counts'] = array_column($data, 'import_success_counts');
        $leftChart['repay_success_counts_day'] = array_column($data, 'repay_success_counts_day');
        $rightChart['pay_success_amount'] = array_column($data, 'pay_success_amount');
        $rightChart['repay_success_amount'] = array_column($data, 'repay_success_amount');
        foreach ($data as $item) {
            $loan = $repay = [];
            foreach ($item as $key => $value) {
                if (in_array($key, self::LOAN_TYPE, true)) {
                    $loan[$key] = $value;
                }
                if (in_array($key, self::REPAY_TYPE, true)) {
                    $repay[$key] = $value;
                }
            }
            $thousandLoanCost = $this->calculateMetric($item['pay_fee_amount'] ?? 0, $item['pay_success_amount'] ?? 0, 1000, 4);
            $thousandRepayCost = $this->calculateMetric($item['repay_fee_amount'] ?? 0, $item['repay_success_amount'] ?? 0, 1000, 4);
            $loanSuccessRate = $this->calculateMetric($item['import_success_counts'] ?? 0, $item['import_counts'] ?? 0, 100);
            $repaySuccessRate = $this->calculateMetric($item['repay_success_counts_day'] ?? 0, $item['repay_counts_day'] ?? 0, 100);
            $leftChart['loan_success_rate'][] = $loanSuccessRate;
            $leftChart['repay_success_rate'][] = $repaySuccessRate;
            $rightChart['thousand_loan_cost'][] = $thousandLoanCost;
            $rightChart['thousand_repay_cost'][] = $thousandRepayCost;

            $repay['thousand_repay_cost'] = $thousandRepayCost;
            $loan['thousand_loan_cost'] = $thousandLoanCost;
            $loan['month'] = $repay['month'] = $item['month'];
            $loanMonth[] = $loan;
            $repayMonth[] = $repay;
        }

        return compact('loanMonth', 'repayMonth', 'leftChart', 'rightChart');
    }
}