<?php

namespace dashboard\controllers;

use dashboard\actions\ChannelRechargeAction;
use dashboard\actions\ChannelSettlementAction;
use dashboard\actions\ReportAccountAmountAction;
use dashboard\actions\ReportAccountBalanceAction;
use dashboard\actions\ReportCompensateAction;
use dashboard\actions\ReportLoanAccountAction;
use dashboard\actions\ReportLoanAction;
use dashboard\actions\ReportMonitorPaySvrAction;
use dashboard\actions\ReportRepayAction;
use dashboard\actions\ReportRepayAfterCompensateAction;
use dashboard\actions\ReportRepayBeforeCompensateAction;
use dashboard\actions\ReportTotalIncomeAction;
use dashboard\actions\ReportTradeAction;
use yii\web\Controller;

/**
 * Class MonitorStatisticsController
 *
 * @package dashboard\controllers
 */
class MonitorStatisticsController extends Controller
{
    public function actions()
    {
        return [
            'account' => ReportAccountAmountAction::class,

            'loan-account' => ReportLoanAccountAction::class, // 放款户统计

            'account-balance' => ReportAccountBalanceAction::class,

            'report-loan-nbfc' => [
                'class'   => ReportLoanAction::class,
                'channel' => 'nbfc_sino',
            ],

            'report-repay-before-compensate-nbfc' => [
                'class'   => ReportRepayBeforeCompensateAction::class,
                'channel' => 'nbfc_sino',
            ],

            'report-repay-after-compensate-nbfc' => [
                'class'   => ReportRepayAfterCompensateAction::class,
                'channel' => 'nbfc_sino',
            ],

            'report-compensate-nbfc' => [
                'class'   => ReportCompensateAction::class,
                'channel' => 'nbfc_sino',
            ],

            'report_trade' => [
                'class' => ReportTradeAction::class,
            ],

            'report-repay' => [
                'class' => ReportRepayAction::class,
            ],

            'channel-settlement' => [
                'class' => ChannelSettlementAction::class,
            ],

            'channel-recharge' => [
                'class' => ChannelRechargeAction::class,
            ],

            'total-income' => [
                'class' => ReportTotalIncomeAction::class,
            ],
            'pay-svr'      => [
                'class' => ReportMonitorPaySvrAction::class,
            ],
        ];
    }
}
