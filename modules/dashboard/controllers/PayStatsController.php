<?php

namespace dashboard\controllers;

use payment\models\paystats\RPCSAllGrantSearch;
use payment\models\paystats\RPCSAllRepaySearch;
use payment\models\paystats\RPCSChannelGrantSearch;
use payment\models\paystats\RPCSChannelRepaySearch;
use payment\models\paystats\RPCSChannelTimeGrantSearch;
use payment\models\paystats\RPCSChannelTimeRepaySearch;
use payment\models\paystats\RPCSFailReasonGrantSearch;
use payment\models\paystats\RPCSFailReasonRepaySearch;
use payment\models\paystats\RPCSPaymentModeGrantSearch;
use payment\models\paystats\RPCSPaymentModeRepaySearch;
use payment\models\paystats\RPCSTimeGrantSearch;
use payment\models\paystats\RPCSTimeRepaySearch;
use payment\models\paystats\SummarizeByMonthSearch;
use Yii;
use yii\filters\AccessControl;
use yii\web\Controller;

/**
 * 支付通道面板搭建
 */
class PayStatsController extends Controller
{
    public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::class,
                'only' => ['index', 'download'],
                'rules' => [
                    [
                        'actions' => ['index', 'download'],
                        'allow' => true,
                        'roles' => ['@'],
                    ],
                ],
            ],
        ];
    }

    public function actionAll(): string
    {
        $searchModel = new RPCSAllGrantSearch();
        $comparisonData = $searchModel->getComparisonData(Yii::$app->request->queryParams);

        return $this->render('index', [
            'viewPath' => 'loan',
            'params' => [
                'searchModel' => $searchModel,
                'comparisonData' => $comparisonData,
            ],
        ]);
    }

    public function actionAllDetail(): string
    {
        $searchModel = new RPCSAllGrantSearch();
        $data = $searchModel->getDetail(Yii::$app->request->queryParams);

        return $this->render('index', [
            'viewPath' => 'loan-detail',
            'params' => [
                'searchModel' => $searchModel,
                'data' => $data,
            ],
        ]);
    }

    public function actionTime(): string
    {
        $searchModel = new RPCSTimeGrantSearch();
        $chartData = $searchModel->getChartData(Yii::$app->request->queryParams);

        return $this->render('index', [
            'viewPath' => 'time-loan',
            'params' => [
                'searchModel' => $searchModel,
                'hourlyLoanData' => $chartData['hourlyLoanData'] ?? [],
                'maxDate' => $chartData['maxDate'] ?? null,
            ]
        ]);
    }

    public function actionFailReason(): string
    {
        $searchModel = new RPCSFailReasonGrantSearch();
        $data = $searchModel->getFailReasonData(Yii::$app->request->queryParams);

        return $this->render('index', [
            'viewPath' => 'fail-reason',
            'params' => [
                'searchModel' => $searchModel,
                'table' => $data['table'] ?? [],
                'chart' => $data['chart'] ?? [],
                'maxDate' => $data['maxDate'] ?? '',
            ]
        ]);
    }


    public function actionFailReasonDetail(): string
    {
        $searchModel = new RPCSFailReasonGrantSearch();
        $data = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('fail-reason-detail', [
            'searchModel' => $searchModel,
            'data' => $data
        ]);
    }

    public function actionChannelAll(): string
    {
        $searchModel = new RPCSChannelGrantSearch();
        $data = $searchModel->getChannelData(Yii::$app->request->queryParams);

        return $this->render('index', [
            'viewPath' => 'channel-all',
            'params' => [
                'searchModel' => $searchModel,
                'table' => $data['table'] ?? [],
                'chart' => $data['chart'] ?? []
            ]
        ]);
    }

    public function actionChannelDetail(): string
    {
        $searchModel = new RPCSChannelGrantSearch();
        [$data, $maxDate] = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('channel-detail', [
            'searchModel' => $searchModel,
            'data' => $data,
            'maxDate' => $maxDate
        ]);
    }


    public function actionChannelTime(): string
    {
        $searchModel = new RPCSChannelTimeGrantSearch();
        $chartData = $searchModel->getChartData(Yii::$app->request->queryParams);

        return $this->render('index', [
            'viewPath' => 'channel-time',
            'params' => [
                'searchModel' => $searchModel,
                'hourlyLoanData' => $chartData['hourlyLoanData'] ?? [],
                'maxDate' => $chartData['maxDate'] ?? null,
            ]
        ]);
    }

    public function actionChannelStats(): string
    {
        $searchModel = new RPCSChannelGrantSearch();
        $data = $searchModel->getChannelData(Yii::$app->request->queryParams);

        return $this->render('index', [
            'viewPath' => 'channel-stats',
            'params' => [
                'searchModel' => $searchModel,
                'table' => $data['table'] ?? [],
                'chart' => $data['chart'] ?? []
            ]
        ]);
    }

    public function actionPaymentModeStats(): string
    {
        $searchModel = new RPCSPaymentModeGrantSearch();
        $data = $searchModel->getPaymentModeData(Yii::$app->request->queryParams);

        return $this->render('index', [
            'viewPath' => 'payment-mode-stats',
            'params' => [
                'searchModel' => $searchModel,
                'table' => $data['table'] ?? [],
                'chart' => $data['chart'] ?? []
            ]
        ]);
    }


    public function actionPaymentModeDetail(): string
    {
        $searchModel = new RPCSPaymentModeGrantSearch();
        [$data, $maxDate] = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('payment-mode-detail', [
            'searchModel' => $searchModel,
            'data' => $data,
            'maxDate' => $maxDate
        ]);
    }


    /**
     * @return string
     */
    public function actionRepayAll(): string
    {
        $searchModel = new RPCSAllRepaySearch();
        $comparisonData = $searchModel->getComparisonData(Yii::$app->request->queryParams);

        return $this->render('index', [
            'viewPath' => 'repay/repay',
            'params' => [
                'searchModel' => $searchModel,
                'comparisonData' => $comparisonData,
            ],
        ]);
    }

    public function actionRepayDetail(): string
    {
        $searchModel = new RPCSAllRepaySearch();
        $data = $searchModel->getDetail(Yii::$app->request->queryParams);

        return $this->render('index', [
            'viewPath' => 'repay/repay-detail',
            'params' => [
                'searchModel' => $searchModel,
                'data' => $data,
            ],
        ]);
    }


    public function actionRepayTime(): string
    {
        $searchModel = new RPCSTimeRepaySearch();
        $chartData = $searchModel->getChartData(Yii::$app->request->queryParams);

        return $this->render('index', [
            'viewPath' => 'repay/time-repay',
            'params' => [
                'searchModel' => $searchModel,
                'hourlyLoanData' => $chartData['hourlyLoanData'] ?? [],
                'maxDate' => $chartData['maxDate'] ?? null,
            ]
        ]);
    }

    public function actionRepayChannelTime(): string
    {
        $searchModel = new RPCSChannelTimeRepaySearch();
        $chartData = $searchModel->getChartData(Yii::$app->request->queryParams);
        return $this->render('index', [
            'viewPath' => 'repay/channel-time',
            'params' => [
                'searchModel' => $searchModel,
                'hourlyLoanData' => $chartData['hourlyLoanData'] ?? [],
                'maxDate' => $chartData['maxDate'] ?? null,
            ]
        ]);
    }

    public function actionRepayFailReason(): string
    {
        $searchModel = new RPCSFailReasonRepaySearch();
        $data = $searchModel->getFailReasonData(Yii::$app->request->queryParams);

        return $this->render('index', [
            'viewPath' => 'repay/fail-reason',
            'params' => [
                'searchModel' => $searchModel,
                'table' => $data['table'] ?? [],
                'chart' => $data['chart'] ?? [],
                'maxDate' => $data['maxDate'] ?? ''
            ]
        ]);
    }

    public function actionRepayFailReasonDetail(): string
    {
        $searchModel = new RPCSFailReasonRepaySearch();
        $data = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('repay/fail-reason-detail', [
            'searchModel' => $searchModel,
            'data' => $data
        ]);
    }

    public function actionRepayChannelAll(): string
    {
        $searchModel = new RPCSChannelRepaySearch();
        $data = $searchModel->getChannelData(Yii::$app->request->queryParams);

        return $this->render('index', [
            'viewPath' => 'repay/channel-all',
            'params' => [
                'searchModel' => $searchModel,
                'table' => $data['table'] ?? [],
                'chart' => $data['chart'] ?? []
            ]
        ]);
    }

    public function actionRepayChannelDetail(): string
    {
        $searchModel = new RPCSChannelRepaySearch();
        [$data, $maxDate] = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('repay/channel-detail', [
            'searchModel' => $searchModel,
            'data' => $data,
            'maxDate' => $maxDate
        ]);
    }

    public function actionRepayPaymentModeStats(): string
    {
        $searchModel = new RPCSPaymentModeRepaySearch();
        $data = $searchModel->getPaymentModeData(Yii::$app->request->queryParams);

        return $this->render('index', [
            'viewPath' => 'repay/payment-mode-stats',
            'params' => [
                'searchModel' => $searchModel,
                'table' => $data['table'] ?? [],
                'chart' => $data['chart'] ?? []
            ]
        ]);
    }


    public function actionRepayPaymentModeDetail(): string
    {
        $searchModel = new RPCSPaymentModeRepaySearch();
        [$data, $maxDate] = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('repay/payment-mode-detail', [
            'searchModel' => $searchModel,
            'data' => $data,
            'maxDate' => $maxDate
        ]);
    }


    public function actionRepayPaymentMethod(): string
    {
        $searchModel = new RPCSPaymentModeRepaySearch();
        $data = $searchModel->getPaymentMethodData(Yii::$app->request->queryParams);

        return $this->render('index', [
            'viewPath' => 'repay/payment-mode-method-stats',
            'params' => [
                'searchModel' => $searchModel,
                'table' => $data['table'] ?? [],
                'chart' => $data['chart'] ?? []
            ]
        ]);
    }


    public function actionSummarizeByMonth(): string
    {
        $searchModel = new SummarizeByMonthSearch();

        $data = $searchModel->search(Yii::$app->request->queryParams);
        return $this->render('index', [
            'viewPath' => 'summary',
            'params' => [
                'searchModel' => $searchModel,
                'leftTable' => $data['loanMonth'] ?? [],
                'rightTable' => $data['repayMonth'] ?? [],
                'leftChart' => $data['leftChart'] ?? [],
                'rightChart' => $data['rightChart'] ?? []
            ]
        ]);
    }

}
