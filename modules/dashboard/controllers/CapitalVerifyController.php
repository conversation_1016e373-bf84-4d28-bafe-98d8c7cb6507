<?php

namespace dashboard\controllers;

use dashboard\models\CapitalVerifySearch;
use kvmanager\models\KeyValue;
use Yii;
use yii\web\Controller;

class CapitalVerifyController extends Controller
{
    protected $queryTypes = [];
    protected $queryKey = 'capital_verify';

    /**
     * @throws \kvmanager\KVException
     */
    public function init(): void
    {
        parent::init();
        $this->queryTypes = KeyValue::take('query_report_asset_amount_types')[$this->queryKey];
    }

    /**
     * @return string
     */
    public function actionIndex(): string
    {
        $searchModel       = new CapitalVerifySearch();
        $params            = Yii::$app->request->get();

        $dataProvider = $searchModel->search($this->queryTypes, $params);

        return $this->render('index', [
            'searchModel'  => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

}

