<?php

namespace dashboard\controllers;

use dashboard\models\DashboardChart;
use dashboard\models\DashboardChartSearch;
use dashboard\models\DashboardTpl;
use datasource\models\DataSource;
use Throwable;
use Yii;
use yii\db\StaleObjectException;
use yii\filters\VerbFilter;
use yii\helpers\Json;
use yii\web\Controller;
use yii\web\NotFoundHttpException;

/**
 * DashboardChartController implements the CRUD actions for DashboardChart model.
 */
class DashboardChartController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class'   => VerbFilter::class,
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all DashboardChart models.
     *
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel  = new DashboardChartSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->get());

        return $this->render('index', [
            'searchModel'  => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Creates a new DashboardChart model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     *
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new DashboardChart();

        $model->tpl_id        = 0;
        $model->time_interval = 86400;
        $model->sort          = 0;

        $model->owner_system = Yii::$app->getRequest()->get('owner_system');

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['index']);
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Updates an existing DashboardChart model.
     * If update is successful, the browser will be redirected to the 'view' page.
     *
     * @param integer $id
     *
     * @return mixed
     * @throws NotFoundHttpException
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['index']);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing DashboardChart model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     *
     * @param integer $id
     *
     * @return mixed
     * @throws NotFoundHttpException
     * @throws Throwable
     * @throws StaleObjectException
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    public function actionGetTemplate($id)
    {
        $tpl = DashboardTpl::findOne($id);
        if (!$tpl) {
            return '{}';
        }

        return $tpl->config;
    }

    /**
     * @return string
     */
    public function actionGetDataSource()
    {
        $ids = (array) Yii::$app->getRequest()->get('id', []);
        if (empty($ids)) {
            return '[]';
        }

        // 自定义模板获取数据逻辑
        $query = DataSource::find()
            ->where([
                'id' => $ids,
            ]);

        $data = [];

        /** @var DataSource $source */
        foreach ($query->each() as $source) {
            $data[$source->id] = $source->makeData();
        }

        return Json::encode($data);
    }

    /**
     * Finds the DashboardChart model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     *
     * @param integer $id
     *
     * @return DashboardChart the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = DashboardChart::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
