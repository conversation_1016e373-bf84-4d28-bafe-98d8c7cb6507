<?php

namespace dashboard\services;

use Carbon\Carbon;
use dashboard\models\ReportAccountAmount;
use dashboard\traits\ReportSearchTrait;
use finance\models\Account;
use yii\db\ActiveQuery;
use yii\db\Expression;

/**
 * Class ReportAccountAmountService
 *
 * @package dashboard\services
 * @property-read Account $account
 */
class ReportAccountAmountService extends ReportAccountAmount
{
    use ReportSearchTrait;

    /**
     * @var int
     * @see Account::$type
     */
    public $accountType;

    public function rules()
    {
        return array_merge([
            [['accountType'], 'default', 'value' => Account::TYPE_VIRTUAL],
            [
                ['account'],
                'filter',
                'filter' => function ($account) {
                    if (empty($account)) {
                        return Account::find()
                            ->where([
                                'type'   => $this->accountType,
                                'status' => Account::STATUS_VALID,
                            ])->andFilterWhere(['like', 'identity', $account])
                            ->select('identity')
                            ->column();
                    }

                    return $account;
                },
            ],
        ], $this->dateGroupRules());
    }

    /**
     * @param array $params
     * @param mixed $types
     *
     * @return ActiveQuery
     */
    public function search($params, $types)
    {
        $this->load($params);
        $this->validate();

        [$startDate, $endDate] = explode(' - ', $this->date_range);

        $query = self::find()
            ->andFilterWhere([
                'and',
                ['>=', 'date', $startDate],
                ['<', 'date', Carbon::parse($endDate)->addDay()->toDateString()],
            ])
            ->andWhere(['type' => $types])
            ->select($this->convertDateGroup($this->date_group))
            ->addSelect(['account'])
            ->groupBy(['dateF', 'account']);

        $selects = [];
        foreach ($types as $type) {
            $selects[$type] = new Expression(sprintf('SUM(IF(`type` = \'%s\', `values`, 0))', $type));
        }
        $query->addSelect($selects);

        return $query;
    }

    /**
     * @param array $params
     * @param array $types
     *
     * @return ActiveQuery
     */
    public function searchReportData($params, $types)
    {
        $query = $this->search($params, $types);
        if (is_array($this->account)) {
            $query->andWhere(['account' => $this->account]);
            $this->account = '';
        } else {
            $query->andFilterWhere(['like', 'account', $this->account]);
        }

        return $query;
    }

    /**
     * @param array $selects
     *
     * @return array
     */
    public function buildGridColumns($selects)
    {
        $columnLists = [
            'dateF' => [
                'attribute' => 'dateF',
                'label'     => '日期',
            ],
        ];

        $columns = [];
        foreach ($selects as $column => $exp) {
            if (!is_string($column)) {
                $column = $exp;
            }

            if (isset($columnLists[$column])) {
                $columns[] = $columnLists[$column];
            }
        }

        return $columns;
    }

    /**
     * @return ActiveQuery
     */
    public function getAccount()
    {
        return $this->hasOne(Account::class, ['identity' => 'account']);
    }
}
