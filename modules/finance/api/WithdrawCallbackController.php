<?php

namespace finance\api;

use finance\worker\AutoWithdrawCallback;
use yii\rest\Controller;
use yii\web\Response;

class WithdrawCallbackController extends Controller
{
    public function actionIndex(): Response
    {
        $task = new AutoWithdrawCallback();
        $res = $task->invoke($this->request->post(), [
            'synctask_from_system' => $this->request->post('from_system'),
        ]);

        return $this->asJson($res);
    }
}
