<?php

namespace finance\worker;

use Carbon\Carbon;
use finance\models\Withdraw;
use RuntimeException;
use system\components\PaymentHttpComponent;
use Throwable;
use waterank\audit\task\LinkCardTvMessageTask;
use xlerr\task\SyncTaskHandler;
use xlerr\task\TaskResult;
use Yii;
use yii\base\InvalidConfigException;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

/**
 * @example {
 * "from_system": "paysvr",
 * "key": "3e60ad85dc2d4c728b085600df2843e9",
 * "type": "withdraw",
 * "data": {
 * "sign": "3cbadd21e8cba9713e93b3e30f87ed4a",
 * "platform_code": "E20000",
 * "platform_message": "SUCCESS",
 * "channel_name": "jazzcash_goldlion_withdraw",
 * "channel_code": "G2P-T-0",
 * "channel_message": "The service request is processed successfully.",
 * "finished_at": "2025-07-29 11:01:11",
 * "channel_key": "JC11321533809",
 * "amount": 1700000,
 * "status": 2,
 * "merchant_key": "P2025072968796141864w",
 * "trade_no": "8adf23d075874ad5a7b49663fafc6358",
 * "wd_number": null,
 * "expire_time": null
 * }
 * }
 */
class AutoWithdrawCallback extends SyncTaskHandler
{
    public function formName(): string
    {
        return 'data';
    }

    public $status;
    public $merchant_key;
    public $trade_no;

    public function rules()
    {
        return [
            [['status', 'merchant_key', 'trade_no'], 'required'],
        ];
    }

    /**
     * @return TaskResult
     * @throws InvalidConfigException
     * @throws Throwable
     */
    public function process(): TaskResult
    {
        return Withdraw::getDb()->transaction(function () {
            $sql = 'select * from dcs_withdraw_order where withdraw_merchant_key = :merchantKey for update skip locked';

            /** @var Withdraw|null $withdraw */
            $withdraw = Withdraw::findBySql($sql, ['merchantKey' => $this->merchant_key])->one();
            if (!$withdraw) {
                throw new RuntimeException('加锁失败');
            }

            if (!in_array($withdraw->withdraw_status, [
                Withdraw::STATUS_SUCCESS,
                Withdraw::STATUS_PAYMENT_ING,
            ], true)) {
                throw new RuntimeException('交易状态错误');
            }

            $paymentClient = PaymentHttpComponent::instance();
            $paymentClient->autoWithdrawQuery($this->merchant_key);

            echo $paymentClient->getRawResponse()->getBody() . PHP_EOL . PHP_EOL;

            $tradeDetails = (array)ArrayHelper::getValue($paymentClient->getData(), 'trade_details');
            $queryResult = array_find($tradeDetails, function ($tradeDetail) {
                return $tradeDetail['trade_no'] === $this->trade_no;
            });
            if (!$queryResult) {
                throw new RuntimeException(vsprintf('订单号[%s]错误: %s', [
                    $this->trade_no,
                    Json::encode($paymentClient->getData()),
                ]));
            }

            $orderStatus = $queryResult['status'] ?? 0;
            if ($orderStatus === 2) {
                $status = Withdraw::STATUS_SUCCESS;
                $info = '';
                $finishAt = Carbon::parse($queryResult['finished_at'])
                    ->setTimezone(Yii::$app->timeZone)
                    ->toDateTimeString();
            } elseif ($orderStatus === 3) {
                $status = Withdraw::STATUS_PAYMENT_FAILED;
                $info = Json::encode([
                    'response' => $queryResult,
                    'error' => $queryResult['channel_message'] . ($withdraw->withdraw_status === Withdraw::STATUS_SUCCESS ? '【冲正】' : ''),
                ]);
                $finishAt = $withdraw->withdraw_finished_at;
            } else {
                throw new RuntimeException('订单状态非终态');
            }

            $result = (int)Withdraw::updateAll([
                'withdraw_status' => $status,
                'withdraw_finished_at' => $finishAt,
                'withdraw_payment_info' => $info,
            ], [
                'withdraw_id' => $withdraw->withdraw_id,
                'withdraw_status' => [Withdraw::STATUS_SUCCESS, Withdraw::STATUS_PAYMENT_ING],
            ]);
            if ($result !== 1) {
                throw new RuntimeException('修改代付状态失败');
            }

            // 付款冲正
            if ($withdraw->withdraw_status === Withdraw::STATUS_SUCCESS && $status === Withdraw::STATUS_PAYMENT_FAILED) {
                if ($withdraw->withdraw_procedure_id) {
                    // 制单冲正
                    CpopOrderReversal::make([
                        'procedureId' => $withdraw->withdraw_procedure_id,
                    ]);
                } elseif ($withdraw->withdraw_create_user_email) {
                    // 手动付款冲正通知
                    LinkCardTvMessageTask::make([
                        'emails' => [$withdraw->withdraw_create_user_email],
                        'title' => '付款申请冲正通知',
                        'content' => vsprintf('您的付款申请[%s]已被冲正', [
                            $withdraw->withdraw_merchant_key,
                        ]),
                        'url' => buildSelfAccessUrl([
                            '/finance/withdraw/index',
                            'withdraw_merchant_key' => $withdraw->withdraw_merchant_key,
                        ]),
                    ]);
                }
            }

            return TaskResult::success();
        });
    }
}
