<?php

namespace finance\worker;

use Carbon\Carbon;
use common\helpers\ArrayHelper;
use finance\models\Withdraw;
use RuntimeException;
use waterank\audit\task\LinkCardTvMessageTask;
use Xlerr\SettlementFlow\Models\Order;
use Xlerr\SettlementFlow\Models\OrderProcedure;
use xlerr\task\TaskHandler;
use xlerr\task\TaskResult;
use yii\helpers\Json;

/**
 * 收付款订单冲正
 */
class CpopOrderReversal extends TaskHandler
{
    public $procedureId;

    public function rules(): array
    {
        return [
            [['procedureId'], 'required'],
        ];
    }

    public function process(): TaskResult
    {
        return OrderProcedure::getDb()->transaction(function () {
            $affected = (int)OrderProcedure::updateAll([
                'status' => OrderProcedure::STATUS_PAUSE,
                'context' => Json::encode([
                    'message' => '交易冲正',
                ]),
            ], [
                'id' => $this->procedureId,
                'status' => OrderProcedure::STATUS_SUCCESS,
            ]);
            if ($affected !== 1) {
                throw new RuntimeException('加锁失败');
            }

            $orderId = OrderProcedure::find()
                ->where(['id' => $this->procedureId])
                ->select('order_id')
                ->scalar();

            $affected = (int)Order::updateAll([
                'status' => Order::STATUS_PROCESSING,
                'updated_at' => Carbon::now()->toDateTimeString(),
            ], [
                'id' => $orderId,
                'status' => [Order::STATUS_SETTLED, Order::STATUS_PROCESSING],
            ]);
            if ($affected !== 1) {
                throw new RuntimeException('修改收付款订单状态失败');
            }

            $result = Withdraw::find()
                ->where([
                    'withdraw_status' => [Withdraw::STATUS_SUCCESS, Withdraw::STATUS_PAYMENT_FAILED],
                    'withdraw_procedure_id' => $this->procedureId,
                ])
                ->select([
                    'withdraw_status',
                    'total' => 'count(*)',
                ])
                ->groupBy('withdraw_status')
                ->asArray()
                ->all();

            $result = ArrayHelper::map($result, 'withdraw_status', 'total');

            $info = vsprintf('共%d笔交易, 成功%d笔, 失败%d笔', [
                array_sum($result),
                $result[Withdraw::STATUS_SUCCESS] ?? 0,
                $result[Withdraw::STATUS_PAYMENT_FAILED] ?? 0,
            ]);

            $affected = (int)OrderProcedure::updateAll([
                'context' => Json::encode([
                    'message' => '交易冲正',
                    'info' => $info,
                ]),
            ], [
                'id' => $this->procedureId,
            ]);

            if ($affected !== 1) {
                throw new RuntimeException('记录冲正信息失败');
            }

            $order = Order::findOne([
                'id' => $orderId,
            ]);

            if ($order && $order->operator) {
                LinkCardTvMessageTask::make([
                    'emails' => [$order->operator->email],
                    'title' => '收付款订单冲正通知',
                    'content' => "付款交易已被冲正($info), 收付款订单状态变更为处理中, 请介入完成该付款申请.",
                    'url' => buildSelfAccessUrl(['/cpop-settlement/order/index', 'orderNo' => $order->order_no]),
                ]);
            }

            return TaskResult::success();
        });
    }
}
