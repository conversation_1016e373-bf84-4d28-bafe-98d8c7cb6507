<?php

namespace EventManager\controllers;

use Carbon\Carbon;
use EventManager\models\BizEvent;
use EventManager\models\EventDaySearch;
use EventManager\models\EventSearch;
use kvmanager\KVException;
use Throwable;
use Yii;
use yii\db\StaleObjectException;
use yii\filters\VerbFilter;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Response;

/**
 * EventController implements the CRUD actions for BizEvent model.
 */
class EventController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors(): array
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all BizEvent models.
     *
     * @return string
     */
    public function actionIndex(): string
    {
        $searchModel = new EventSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * @param string|null $start
     * @param string|null $end
     *
     * @return Response|string
     * @throws KVException
     */
    public function actionFullcalendar(string $start = null, string $end = null)
    {
        if ($start && $end) {
            $data = EventSearch::eventList(Carbon::parse($start), Carbon::parse($end));

            return $this->asJson($data);
        }

        return $this->render('fullcalendar');
    }

    /**
     * @return string
     */
    public function actionDetail(): string
    {
        $searchModel = new EventDaySearch();
        $events = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('detail', [
            'model' => $searchModel,
            'events' => $events,
        ]);
    }

    /**
     * Creates a new BizEvent model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     *
     * @param null $date
     *
     * @return string
     * @throws KVException
     */
    public function actionCreate($date = null): string
    {
        $model = new BizEvent();
        if ($this->request->isPost) {
            $model->load($this->request->post());
            if ($model->save()) {
                return <<<JAVASCRIPT
<script>window.parent.closeNewCalendarLayer()</script>
JAVASCRIPT;
            }
        }

        $model->event_date = Carbon::parseFromLocale(current(explode(' ', $date ?? 'now')))->toDateTimeString();
        $model->event_author = Yii::$app->user->identity->username ?? '';

        $config = BizEvent::config();

        return $this->render('create', [
            'model' => $model,
            'config' => $config,
        ]);
    }

    /**
     * Updates an existing BizEvent model.
     * If update is successful, the browser will be redirected to the 'view' page.
     *
     * @param int $id
     *
     * @return string|Response
     * @throws NotFoundHttpException
     */
    public function actionUpdate(int $id)
    {
        $model = $this->findModel($id);

        if ($model->load($this->request->post()) && $model->save()) {
            return $this->redirect(['index']);
        }
        $model->event_date .= ' ' . $model->event_time;

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing BizEvent model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     *
     * @param int $id
     *
     * @return Response
     * @throws NotFoundHttpException
     * @throws Throwable
     * @throws StaleObjectException
     */
    public function actionDelete(int $id): Response
    {
        $this->findModel($id)->delete();

        return $this->redirect($this->request->getReferrer());
    }

    /**
     * Finds the BizEvent model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     *
     * @param int $id
     *
     * @return BizEvent the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel(int $id): BizEvent
    {
        if (($model = BizEvent::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
