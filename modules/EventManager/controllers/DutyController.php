<?php

namespace EventManager\controllers;

use Carbon\Carbon;
use EventManager\models\Duty;
use yii\helpers\ArrayHelper;
use yii\web\Controller;
use yii\web\Response;

/**
 * DutyController implements the CRUD actions for Duty model.
 */
class DutyController extends Controller
{
    public function actionCalendar(string $start, string $end): Response
    {
        $records = Duty::find()
            ->where([
                'and',
                ['>=', 'date', Carbon::parse($start)->toDateString()],
                ['<', 'date', Carbon::parse($end)->toDateString()],
            ])
            ->orderBy(['date' => SORT_ASC])
            ->all();

        /** @var array<string, array<Duty>> $records */
        $records = ArrayHelper::index($records, null, [
            static function (Duty $duty) {
                return vsprintf('%s: %s', [
                    $duty->system,
                    $duty->username,
                ]);
            },
        ]);

        $groups = Duty::groups();

        $data = [];
        foreach ($records as $group) {
            $date = null;
            foreach ($group as $duty) {
                if ($date === null || strtotime($date) + 86400 !== strtotime($duty->date)) {
                    $data[] = [
                        // 'editable' => true, // 允许拖动
                        'start' => $duty->date,
                        'title' => vsprintf('%s值班: %s', [
                            $groups[$duty->system] ?? $duty->system,
                            $duty->username,
                        ]),
                        // 'color' => '#00c0ef',
                        'color' => Duty::groupColor($duty->system),
                    ];
                } else {
                    $data[array_key_last($data)]['end'] = Carbon::parse($duty->date)->addDay()->toDateString();
                }
                $date = $duty->date;
            }
        }

        return $this->asJson($data);
    }

    /**
     * Creates a new Duty model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     *
     * @return Response|string
     */
    public function actionCreate()
    {
        $model = new Duty();
        if ($model->load($this->request->post()) && $model->submit()) {
            // return Html::script('window.top.reloadCurrentTab()');
            return $this->redirect(['create']);
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }
}
