<?php

namespace EventManager\models;

use Carbon\Carbon;
use kvmanager\KVException;
use yii\data\ActiveDataProvider;
use yii\db\Expression;
use yii\helpers\Url;

/**
 * EventSearch represents the model behind the search form about `backend\models\BizEvent`.
 */
class EventSearch extends BizEvent
{
    public ?string $start_date = null;
    public ?string $end_date = null;

    /**
     * @return string
     */
    public function formName(): string
    {
        return '';
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [['event_id', 'event_year'], 'integer'],
            [
                [
                    'event_name',
                    'event_content',
                    'event_month',
                    'event_date',
                    'event_create_at',
                    'event_update_at',
                    'event_from_system',
                    'event_author',
                    'start_date',
                    'end_date',
                    'event_level',
                    'event_tags',
                ],
                'safe',
            ],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = self::find()->select([
            '*',
            'event_content' => new Expression('left(event_content, 20)'),
        ]);

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort'  => [
                'attributes'   => [
                    'event_date' => [
                        'desc' => [
                            'event_date' => SORT_DESC,
                            'event_time' => SORT_DESC,
                        ],
                        'asc'  => [
                            'event_date' => SORT_ASC,
                            'event_time' => SORT_ASC,
                        ],
                    ],
                    'event_create_at',
                ],
                'defaultOrder' => ['event_date' => SORT_DESC],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'event_id'        => $this->event_id,
            'event_year'      => $this->event_year,
            'event_date'      => $this->event_date,
            'event_create_at' => $this->event_create_at,
            'event_update_at' => $this->event_update_at,
        ]);
        $query->andFilterWhere(['between', 'event_date', $this->start_date, $this->end_date]);
        $query->andFilterWhere(['like', 'event_name', $this->event_name])
            ->andFilterWhere(['like', 'event_tags', $this->event_tags])
            ->andFilterWhere(['=', 'event_from_system', $this->event_from_system])
            ->andFilterWhere(['like', 'event_author', $this->event_author])
            ->andFilterWhere(['=', 'event_level', $this->event_level]);

        return $dataProvider;
    }

    /**
     * @param Carbon $start
     * @param Carbon $end
     *
     * @return array
     * @throws KVException
     */
    public static function eventList(Carbon $start, Carbon $end): array
    {
        $query = self::find()
            ->where([
                'and',
                ['>=', 'event_date', $start->toDateString()],
                ['<', 'event_date', $end->toDateString()],
            ])
            ->select([
                'event_id',
                'event_name',
                'event_date',
                'event_time',
                'event_level',
                'event_from_system',
            ]);

        $colors = [
            'blue'   => '#0073b7',
            'aqua'   => '#00c0ef',
            'yellow' => '#f39c12',
            'red'    => '#dd4b39',
        ];

        $config = self::config();

        $events = [];
        /** @var self $event */
        foreach ($query->each() as $event) {
            $events[] = [
                'id'     => $event->event_id,
                'title'  => vsprintf('%s %s', [
                    $config['event_system'][$event->event_from_system] ?? '-',
                    $event->event_name,
                ]),
                'start'  => vsprintf('%s %s', [$event->event_date, $event->event_time]),
                'color'  => $colors[$event->color()] ?? $colors['blue'],
                'allDay' => $event->event_time === '00:00:00',
                'url'    => Url::to([
                    'detail',
                    'event_date' => $event->event_date,
                    '#'          => 'id_' . $event->event_id,
                ]),
            ];
        }

        return $events;
    }
}
