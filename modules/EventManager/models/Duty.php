<?php

namespace EventManager\models;

use Carbon\Carbon;
use Exception;
use kvmanager\models\KeyValue;
use Yii;
use yii\base\UserException;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "duty".
 *
 * @property int $id         ID
 * @property string $date       日期
 * @property string $system     系统
 * @property string $username   姓名
 * @property string $created_at 创建时间
 * @property string $updated_at 更新时间
 */
class Duty extends ActiveRecord
{
    public $startDate;
    public $endDate;
    /**
     * @var array
     */
    public $users;

    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return 'duty';
    }

    /**
     * @return array
     */
    public static function groups(): array
    {
        $config = KeyValue::take('duty_config');

        return (array)($config['groups'] ?? []);
    }

    public static function groupColor(string $group): string
    {
        $config = KeyValue::take('duty_config');

        return $config['group_color'][$group] ?? '#39cccc';
    }

    public function formName(): string
    {
        return '';
    }

    public function behaviors(): array
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'value' => fn() => Carbon::now()->toDateTimeString(),
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['startDate', 'endDate', 'system', 'username'], 'required'],
            [['system'], 'string', 'max' => 32],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'id' => 'ID',
            'date' => '日期',
            'system' => '群组',
            'username' => '值班人',
            'created_at' => '创建时间',
            'updated_at' => '更新时间',
            'startDate' => '开始日期',
            'endDate' => '结束日期',
        ];
    }

    public function submit(): bool
    {
        if (!$this->validate()) {
            return false;
        }
        $startDate = Carbon::parse($this->startDate);
        $endDate = Carbon::parse($this->endDate);
        $this->users = $this->username;

        $session = Yii::$app->session;
        $transaction = self::getDb()->beginTransaction();
        try {
            $deleteData = [];
            $insertData = [];
            while ($startDate <= $endDate) {
                $deleteData[] = $startDate->toDateString();
                foreach ($this->users as $username) {
                    $insertData[] = [
                        $startDate->toDateString(),
                        $this->system,
                        $username,
                    ];
                }
                $startDate->addDay();
            }
            Duty::deleteAll(['date' => $deleteData, 'system' => $this->system]);
            $count = Duty::getDb()->createCommand()
                ->batchInsert(self::tableName(), ['date', 'system', 'username'], $insertData)
                ->execute();
            if ($count < 1) {
                throw new UserException('值班配置插入失败,请稍后再试!');
            }
            $transaction->commit();
            $session->addFlash('success', '保存成功');

            return true;
        } catch (Exception $e) {
            $transaction->rollBack();
            $session->addFlash('error', $e->getMessage());

            return false;
        }
    }
}
