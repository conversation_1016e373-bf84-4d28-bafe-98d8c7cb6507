<?php

namespace EventManager\models;

use Carbon\Carbon;

/**
 * EventSearch represents the model behind the search form about `backend\models\BizEvent`.
 */
class EventDaySearch extends BizEvent
{
    public function formName(): string
    {
        return '';
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [['event_date'], 'default', 'value' => Carbon::now()->toDateString()],
            [['event_level', 'event_from_system', 'event_tags'], 'safe'],
        ];
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return array|EventDaySearch[]
     */
    public function search(array $params): array
    {
        $this->load($params);
        $this->validate();
        $query = self::find()
            ->orderBy('event_time')
            ->where([
                'event_date' => $this->event_date,
            ])
            ->andFilterWhere([
                'event_level'       => $this->event_level,
                'event_from_system' => $this->event_from_system,
            ])
            ->andFilterWhere(['like', 'event_tags', $this->event_tags]);

        return $query->all();
    }
}
