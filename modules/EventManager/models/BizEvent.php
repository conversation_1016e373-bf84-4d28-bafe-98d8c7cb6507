<?php

namespace EventManager\models;

use Carbon\Carbon;
use kvmanager\KVException;
use kvmanager\models\KeyValue;
use Yii;
use yii\behaviors\BlameableBehavior;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "biz_event".
 *
 * @property integer $event_id
 * @property string  $event_name
 * @property string  $event_content
 * @property string  $event_image
 * @property integer $event_year
 * @property string  $event_month
 * @property string  $event_date
 * @property string  $event_time
 * @property string  $event_create_at
 * @property string  $event_update_at
 * @property string  $event_from_system
 * @property string  $event_author
 * @property string  $event_level
 * @property string  $event_user_id
 * @property string  $event_tags
 */
class BizEvent extends ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName(): string
    {
        return '{{%biz_event}}';
    }

    public function behaviors(): array
    {
        return [
            [
                'class'              => BlameableBehavior::class,
                'createdByAttribute' => 'event_user_id',
                'updatedByAttribute' => null,
            ],
            [
                'class'              => BlameableBehavior::class,
                'createdByAttribute' => 'event_author',
                'updatedByAttribute' => null,
                'value'              => function () {
                    return Yii::$app->user->identity->username ?? '';
                },
            ],
            [
                'class'              => TimestampBehavior::class,
                'createdAtAttribute' => 'event_create_at',
                'updatedAtAttribute' => 'event_update_at',
                'value'              => function () {
                    return Carbon::now()->toDateTimeString();
                },
            ],
        ];
    }

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [
                [
                    'event_name',
                    'event_content',
                    'event_date',
                    'event_level',
                    'event_from_system',
                ],
                'required',
            ],
            [['event_name'], 'string', 'max' => 200],
            [['event_tags'], 'string', 'max' => 255],
            [
                'event_year',
                'filter',
                'filter' => function ($value) {
                    return Carbon::parse($this->event_date)->year;
                },
            ],
            [
                'event_month',
                'filter',
                'filter' => function ($value) {
                    return Carbon::parse($this->event_date)->format('Y-m');
                },
            ],
            [
                'event_time',
                'filter',
                'filter' => function ($value) {
                    return Carbon::parse($this->event_date)->toTimeString();
                },
            ],
            [
                ['event_date'],
                'filter',
                'filter' => function ($v) {
                    return Carbon::parse($v)->toDateString();
                },
            ],
            [['event_from_system'], 'in', 'range' => array_keys(self::config()['event_system'] ?? [])],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'event_id'          => 'ID',
            'event_name'        => '名称',
            'event_content'     => '内容',
            'event_year'        => '事件所属年',
            'event_image'       => '事件图片',
            'event_month'       => '事件所属月',
            'event_date'        => '时间',
            'event_create_at'   => '创建时间',
            'event_update_at'   => '更新时间',
            'event_from_system' => '系统',
            'event_author'      => '添加人员',
            'event_level'       => '事件类型',
            'event_time'        => '发生时间',
            'event_tags'        => '标签',
        ];
    }

    /**
     * @return array
     * @throws KVException
     */
    public static function config(): array
    {
        static $config;
        if (!$config) {
            $config = KeyValue::takeAsArray('biz_event_manager_config');
        }

        return $config;
    }

    /**
     * @return string
     * @throws KVException
     */
    public function color(): string
    {
        $config = self::config();

        return $config['event_level'][$this->event_level]['color'] ?? 'blue';
    }
}
