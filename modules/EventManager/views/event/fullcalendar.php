<?php

use EventManager\models\EventSearch;
use xlerr\fullcalendar\FullcalendarWidget;
use yii\helpers\Url;
use yii\web\JsExpression;
use yii\web\View;

/* @var $this View */
/* @var $searchModel EventSearch */
/* @var $events array */

$this->title = '日历';

?>
<style>
    .fc-toolbar {
        padding: 0 !important;
    }
</style>
<div style="padding: 10px; background-color: #fff; box-shadow: 0 0 5px rgba(0, 0, 0, .125)">
    <?= FullcalendarWidget::widget([
        'pluginOptions' => [
            'customButtons' => [
                'duty' => [
                    'text' => '值班配置',
                    'click' => new JsExpression(
                        '
function() {
    window.makeDialog({
        type: 2,
        title: \'值班配置\',
        content: \'/event/duty/create?date=now\',
    })
}'
                    ),
                ],
            ],
            'headerToolbar' => [
                'end' => 'dayGridMonth,timeGridWeek,timeGridDay,listWeek duty',
            ],
            'eventTimeFormat' => [ // like '14:30:00'
                'hour' => '2-digit',
                'minute' => '2-digit',
                'hour12' => false,
            ],
            //                'aspectRatio' => 2.14,
            'dayMaxEventRows' => true, // for all non-TimeGrid views
            // 'contentHeight' => 1000,
            'eventSources' => [
                Url::to(['/event/duty/calendar']),
            ],
        ],
    ]) ?>
</div>
