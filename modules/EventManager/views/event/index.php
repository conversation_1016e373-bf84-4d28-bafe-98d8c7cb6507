<?php

use EventManager\models\BizEvent;
use EventManager\models\EventSearch;
use xlerr\common\grid\DialogActionColumn;
use xlerr\common\widgets\GridView;
use yii\data\ActiveDataProvider;
use yii\helpers\Html;
use yii\web\View;

/** @var $this View */
/** @var $searchModel EventSearch */
/** @var $dataProvider ActiveDataProvider */

$this->title = '事件列表';

$this->params['breadcrumbs'][] = $this->title;

$config = BizEvent::config();
$levels = [];
foreach ((array)($config['event_level'] ?? []) as $level => $options) {
    $levels[$level] = $options['label'] ?? '-';
}

echo $this->render('_search', [
    'model'  => $searchModel,
    'config' => $config,
    'action' => 'index',
]);

echo GridView::widget([
    'dataProvider' => $dataProvider,
    'emptyText'    => '暂无事件记录',
    'columns'      => [
        [
            'class'    => DialogActionColumn::class,
            'template' => '{update} {delete}',
        ],

        'event_id',
        'event_date:raw:日期',
        'event_time:raw:时间',
        'event_name',
        [
            'attribute' => 'event_content',
            'format'    => static fn($v) => Html::encode($v),
        ],
        [
            'attribute' => 'event_tags',
            'format'    => ['truncate', 10],
        ],
        [
            'attribute' => 'event_level',
            'format'    => ['in', $levels],
        ],
        [
            'attribute' => 'event_from_system',
            'format'    => ['in', $config['event_system'] ?? []],
        ],
        'event_author',
        'event_create_at',
    ],
]);
