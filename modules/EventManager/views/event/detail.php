<?php

use xlerr\common\assets\ShowdownJsAsset;
use Carbon\Carbon;
use xlerr\common\widgets\DatePicker;
use EventManager\models\BizEvent;
use EventManager\models\EventDaySearch;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/** @var $this View */
/** @var $model EventDaySearch */
/** @var $events EventDaySearch */
/** @var $week_days array */
/** @var $now_date array */

ShowdownJsAsset::register($this);

$this->title = '事件墙';

$this->params['breadcrumbs'][] = $this->title;

$config = BizEvent::config();
?>
<style>
    .timeline-footer a {
        display: inline-block;
        text-align: center;
    }

    .mdcontent {
        width: 100%;
    }

    .mdcontent img {
        max-width: 100%;
    }
</style>
<div class="box box-default">
    <div class="box-header with-border">
        <h1 class="box-title">搜索</h1>
    </div>

    <?php
    $form = ActiveForm::begin([
        'action'        => ['detail'],
        'method'        => 'get',
        'type'          => ActiveForm::TYPE_INLINE,
        'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
    ]); ?>
    <div class="box-body">

        <?= $form->field($model, 'event_from_system')->widget(Select2::class, [
            'data'          => $config['event_system'],
            'hideSearch'    => true,
            'options'       => [
                'placeholder' => '选择来源系统',
            ],
            'pluginOptions' => [
                'allowClear' => true,
            ],
        ]) ?>

        <?= $form->field($model, 'event_level')->widget(Select2::class, [
            'data'          => array_column($config['event_level'], 'label'),
            'hideSearch'    => true,
            'options'       => [
                'placeholder' => '选择事件级别',
            ],
            'pluginOptions' => [
                'allowClear' => true,
            ],
        ]) ?>

        <?= $form->field($model, 'event_date')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '事件所属日',
            ],
        ]) ?>

        <?= $form->field($model, 'event_tags')->textInput([
            'placeholder' => '事件标签',
        ]) ?>

        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>
        <?= Html::a('返回', ['index'], ['class' => 'btn btn-default']) ?>

        <?php
        ActiveForm::end(); ?>

    </div>
</div>

<?php

if (empty($events)) {
    $events = [
        new EventDaySearch([
            'event_id'          => 0,
            'event_time'        => Carbon::now()->toTimeString(),
            'event_author'      => '-',
            'event_name'        => '',
            'event_level'       => 'info',
            'event_content'     => '无数据',
            'event_from_system' => null,
        ]),
    ];
}

$config    = BizEvent::config();
$systemMap = (array)($config['event_system'] ?? []);
$content   = '';
foreach ($events as $event) {
    $content .= $this->render('_time_item', [
        'event'     => $event,
        'systemMap' => $systemMap,
    ]);
}
?>
<ul class="timeline">
    <li class="time-label"><span class="bg-default"><?= $model->event_date ?></span></li>
    <?= $content ?>
    <li>
        <i class="fa fa-clock-o bg-gray"></i>
    </li>
</ul>
<script>
    <?php $this->beginBlock('markdown') ?>
    const converter = new showdown.Converter({
        omitExtraWLInCodeBlocks: false,
        noHeaderId: false,
        prefixHeaderId: false,
        rawPrefixHeaderId: false,
        ghCompatibleHeaderId: false,
        rawHeaderId: false,
        headerLevelStart: false,
        parseImgDimensions: false,
        simplifiedAutoLink: false,
        excludeTrailingPunctuationFromURLs: false,
        literalMidWordUnderscores: false,
        literalMidWordAsterisks: false,
        strikethrough: false,
        tables: true,
        tablesHeaderId: true,
        ghCodeBlocks: true,
        tasklists: true,
        smoothLivePreview: true,
        smartIndentationFix: true,
        disableForced4SpacesIndentedSublists: false,
        simpleLineBreaks: true,
        requireSpaceBeforeHeadingText: false,
        ghMentions: true,
        ghMentionsLink: 'https://github.com/{u}',
        encodeEmails: true,
        openLinksInNewWindow: true,
        backslashEscapesHTMLTags: false,
        emoji: false,
        underline: true,
        completeHTMLDocument: false,
        metadata: true,
        splitAdjacentBlockquotes: false
    });
    $('.timeline-body > div.mdcontent').each(function () {
        let section = $(this);
        section.html(converter.makeHtml(section.text().trim()));
    });
    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['markdown']) ?>
</script>
