<?php

use EventManager\models\EventSearch;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model EventSearch */
/* @var $form ActiveForm */
?>

<div class="box box-default search">
    <div class="box-header with-border">
        <div class="box-title">搜索</div>
    </div>

    <div class="box-body">
        <?php
        $form = ActiveForm::begin([
            'action' => [''],
            'method' => 'get',
            'type' => ActiveForm::TYPE_INLINE,
            'waitingPrompt' => ActiveForm::WAITING_PROMPT_SEARCH,
        ]) ?>

        <?= $form->field($model, 'event_from_system', [
            'options' => [
                'class' => 'form-group',
                'style' => 'min-width:150px',
            ],
        ])->widget(Select2::class, [
            'data' => $config['event_system'] ?? [],
            'hideSearch' => true,
            'options' => [
                'placeholder' => '选择来源系统',
            ],
            'pluginOptions' => [
                'allowClear' => true,
            ],
        ]) ?>

        <?= $form->field($model, 'event_level', [
            'options' => [
                'class' => 'form-group',
                'style' => 'min-width:150px',
            ],
        ])->widget(Select2::class, [
            'data' => $config['event_level']['label'] ?? [],
            'hideSearch' => true,
            'pluginOptions' => [
                'allowClear' => true,
            ],
            'options' => [
                'placeholder' => '选择事件级别',
            ],
        ]) ?>

        <?= $form->field($model, 'event_author')->textInput([
            'placeholder' => '添加人员',
        ]) ?>

        <?= $form->field($model, 'event_name')->textInput([
            'placeholder' => '事件名称',
        ]) ?>

        <?= $form->field($model, 'event_tags') ?>

        <?= $form->field($model, 'start_date')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '开始时间',
            ],
        ]) ?>

        <?= $form->field($model, 'end_date')->widget(DatePicker::class, [
            'options' => [
                'placeholder' => '结束时间',
            ],
        ]) ?>

        <?= Html::submitButton('<i class="fa fa-search"></i> 搜索', ['class' => 'btn btn-primary']) ?>
        <?= Html::a('重置搜索条件', [''], ['class' => 'btn btn-default']) ?>
        <?= Html::a('新事件', ['create'], [
            'class' => 'btn btn-success layer-dialog',
        ]) ?>

        <?php
        ActiveForm::end(); ?>

    </div>
</div>
