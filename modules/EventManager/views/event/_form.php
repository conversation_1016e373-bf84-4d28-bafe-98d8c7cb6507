<?php

use backend\assets\PasteHandlerAsset;
use xlerr\common\assets\ShowdownJsAsset;
use EventManager\models\BizEvent;
use kartik\widgets\DateTimePicker;
use xlerr\CodeEditor\CodeEditor;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

$config = BizEvent::config();
$levels = [];
foreach ($config['event_level'] as $level => $options) {
    $levels[$level] = $options['label'] ?? 'unknow';
}

/* @var $this View */
/* @var $model BizEvent */
/* @var $form ActiveForm */

ShowdownJsAsset::register($this);
PasteHandlerAsset::register($this);

$form = ActiveForm::begin([
    'method' => 'post',
]);
?>
<style>
    .markdownWrap {
        height: 406px;
        padding: 10px;
        border: 1px dashed #efefef;
        border-right: 0 none;
        border-radius: 0;
        word-wrap: break-word;
        word-break: normal;
        overflow-y: scroll;
        width: 100%;
    }

    #preview {
        width: 100%;
    }

    #preview img {
        max-width: 100%;
    }
</style>
<div class="row">
    <div class="col-xs-4">
        <?= $form->field($model, 'event_from_system')->widget(Select2::class, [
            'data'       => $config['event_system'] ?? [],
            'hideSearch' => true,
        ]) ?>
    </div>
    <div class="col-xs-4">
        <?= $form->field($model, 'event_level')->widget(Select2::class, [
            'data'       => $levels,
            'hideSearch' => true,
        ]) ?>
    </div>
    <div class="col-xs-4">
        <?= $form->field($model, 'event_date')->widget(DateTimePicker::class, [
            'type'          => DateTimePicker::TYPE_INPUT,
            'pluginOptions' => [
                'todayBtn'       => 'linked',
                'todayHighlight' => true,
                'autoclose'      => true,
            ],
        ]) ?>
    </div>
</div>

<div class="row">
    <div class="col-xs-6">
        <?= $form->field($model, 'event_name') ?>
    </div>
    <div class="col-xs-6">
        <?= $form->field($model, 'event_tags')->textInput([
            'placeholder' => '以英文逗号间隔',
        ]) ?>
    </div>
</div>

<div class="row">
    <div class="col-xs-6">
        <?= $form->field($model, 'event_content')->widget(CodeEditor::class, [
            'clientOptions' => [
                'mode'     => CodeEditor::MODE_Markdown,
                'minLines' => 25,
                'maxLines' => 25,
            ],
        ]) ?>
    </div>
    <div class="col-xs-6">
        <label for="">预览: </label>
        <div class="markdownWrap">
            <div id="preview"></div>
        </div>
    </div>
</div>

<?= Html::submitButton('保存', [
    'class' => 'btn btn-primary',
]) ?>

<?php
ActiveForm::end(); ?>
<script>
    <?php $this->beginBlock('showdownjs') ?>
    (function () {
        let timer
        const showdownConverter = new showdown.Converter({
                omitExtraWLInCodeBlocks: false,
                noHeaderId: false,
                prefixHeaderId: false,
                rawPrefixHeaderId: false,
                ghCompatibleHeaderId: false,
                rawHeaderId: false,
                headerLevelStart: false,
                parseImgDimensions: false,
                simplifiedAutoLink: false,
                excludeTrailingPunctuationFromURLs: false,
                literalMidWordUnderscores: false,
                literalMidWordAsterisks: false,
                strikethrough: false,
                tables: true,
                tablesHeaderId: true,
                ghCodeBlocks: true,
                tasklists: true,
                smoothLivePreview: true,
                smartIndentationFix: true,
                disableForced4SpacesIndentedSublists: false,
                simpleLineBreaks: true,
                requireSpaceBeforeHeadingText: false,
                ghMentions: true,
                ghMentionsLink: 'https://github.com/{u}',
                encodeEmails: true,
                openLinksInNewWindow: true,
                backslashEscapesHTMLTags: false,
                emoji: false,
                underline: true,
                completeHTMLDocument: false,
                metadata: true,
                splitAdjacentBlockquotes: false
            }),
            previewBox = document.getElementById('preview'),
            editorId = '<?= Html::getInputId($model, 'event_content') ?>',
            aceEditor = aceInstance[editorId],
            pasteHandler = new PasteHandler(function (data) {
                if (/^data:image\/jpeg;base64,/.test(data)) {
                    let key = 'img:' + PasteHandler.hash(data)
                    data = `![${key}](${data})`
                } else if (/^https?:\/\//.test(data)) {
                    data = `[${data}](${data})`
                }
                aceEditor.insert(data)
            }),
            change = () => {
                window.clearTimeout(timer)
                timer = window.setTimeout(() => {
                    previewBox.innerHTML = showdownConverter.makeHtml(aceEditor.getValue())
                }, 500)
            }

        aceEditor.textInput.getElement().onpaste = (e) => pasteHandler.paste(e)
        aceEditor.onPaste = () => {
            // 粘贴图片时，防止将文件名粘贴进去
        }

        aceEditor.session.on('change', change)
        change()
    })();
    <?php $this->endBlock() ?>
    <?php $this->registerJs($this->blocks['showdownjs']) ?>
</script>
