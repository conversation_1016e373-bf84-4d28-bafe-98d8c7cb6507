<?php

use EventManager\models\EventDaySearch;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $event EventDaySearch */
/* @var $systemMap array */

$tags = array_map(function ($tag) {
    return Html::tag('span', $tag, [
        'class' => 'label label-warning',
    ]);
}, (array)explode(',', $event->event_tags));
?>
<li>
    <i class="fa fa-comments bg-<?= $event->color() ?>"></i>
    <div class="timeline-item" id="id_<?= $event->event_id ?>">
        <span class="time"><i class="fa fa-clock-o"></i> <?= $event->event_time ?></span>
        <h3 class="timeline-header">
            <span class="text-primary"><?= $event->event_author ?></span> <?= $event->event_name ?>
        </h3>
        <?php
        $content_is_show = mb_strlen(strip_tags($event->event_content)) > 128 ?>
        <div class="timeline-body">
            <div class="mdcontent">
                <?= $event->event_content ?>
            </div>
            <?= Yii::$app->user->id == $event->event_user_id ? Html::a('编辑', [
                'update',
                'id' => $event->event_id,
            ], [
                'target' => '_blank',
            ]) : '' ?>
        </div>
        <?php
        if ($event->event_id) : ?>
            <div class="timeline-footer">
                <div style="display:flex; justify-content:space-between">
                    <div class="event_tag">标签: <?= implode(' ', $tags) ?></div>
                    <div>来源：<?= $systemMap[$event->event_from_system] ?? '-' ?></div>
                </div>
            </div>
        <?php
        endif; ?>
    </div>
</li>