<?php

use Carbon\Carbon;
use common\models\User;
use EventManager\models\Duty;
use xlerr\common\widgets\ActiveForm;
use xlerr\common\widgets\DatePicker;
use xlerr\common\widgets\Select2;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model Duty */
?>

<?php $form = ActiveForm::begin(); ?>

<div class="box-body">

    <div class="row">
        <div class="col-xs-6">
            <?= $form->field($model, 'system')->widget(Select2::class, [
                'data' => Duty::groups(),
                'options' => [
                    'prompt' => $model->getAttributeLabel('system'),
                ],
            ]) ?>

            <?= $form->field($model, 'startDate')->widget(DatePicker::class, [
                'pluginOptions' => [
                    'todayBtn' => false,
                    'startDate' => Carbon::now()->toDateString(),
                ],
            ]) ?>
        </div>
        <div class="col-xs-6">
            <?= $form->field($model, 'username')->widget(Select2::class, [
                'data' => User::list('username'),
                'pluginOptions' => [
                    'allowClear' => true,
                    'multiple' => true,
                ],
                'options' => [
                    'placeholder' => $model->getAttributeLabel('username'),
                ],
            ]) ?>

            <?= $form->field($model, 'endDate')->widget(DatePicker::class, [
                'pluginOptions' => [
                    'todayBtn' => false,
                    'startDate' => Carbon::now()->toDateString(),
                ],
            ]) ?>
        </div>
    </div>
</div>

<div class="box-footer">
    <?= Html::submitButton('保存', [
        'class' => 'btn btn-primary',
    ]) ?>
</div>

<?php ActiveForm::end(); ?>
