$(function () {

    const chartLeft = echarts.init(document.getElementById('chart-left'));
    const chartRight = echarts.init(document.getElementById('chart-right'));
    const left = {$left};
    const right = {$right};
    // 左图（放款/还款笔数 + 成功率）
    chartLeft.setOption({
        title: {text: '放款 / 还款笔数趋势'},
        tooltip: {trigger: 'axis'},
        legend: {data: ['放款笔数', '还款笔数', '放款成功率', '还款成功率']},
        xAxis: {type: 'category', data: data.dates},
        yAxis: [
            {type: 'value', name: '笔数'},
            {type: 'value', name: '成功率', min: 0, max: 1}
        ],
        series: [
            {type: 'bar', name: '放款笔数', data: left.loanCounts},
            {type: 'bar', name: '还款笔数', data: left.repayCounts},
            {type: 'line', name: '放款成功率', yAxisIndex: 1, data: left.loanSuccessRates},
            {type: 'line', name: '还款成功率', yAxisIndex: 1, data: left.repaySuccessRates}
        ]
    });

    // 右图（放款/还款金额 + 千元成本）
    chartRight.setOption({
        title: {text: '放款 / 还款金额趋势'},
        tooltip: {trigger: 'axis'},
        legend: {data: ['放款金额', '还款金额', '放款成本', '还款成本']},
        xAxis: {type: 'category', data: data.dates},
        yAxis: [
            {type: 'value', name: '金额 (K)'},
            {type: 'value', name: '千元成本'}
        ],
        series: [
            {type: 'bar', name: '放款金额', data: right.loanAmounts},
            {type: 'bar', name: '还款金额', data: right.repayAmounts},
            {type: 'line', name: '放款成本', yAxisIndex: 1, data: right.loanCostPerK},
            {type: 'line', name: '还款成本', yAxisIndex: 1, data: right.repayCostPerK}
        ]
    });

});