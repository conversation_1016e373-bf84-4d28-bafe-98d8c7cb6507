{"name": "biz_global/portal", "description": "biz global portal", "type": "project", "license": "proprietary", "support": {"issues": "https://git.kuainiujinke.com/biz_global/portal/issues", "wiki": "https://git.kuainiujinke.com/biz_global/portal/wikis/home", "source": "https://git.kuainiujinke.com/biz_global/portal"}, "require": {"php": ">=7.4", "ext-bcmath": "*", "ext-curl": "*", "ext-intl": "*", "ext-json": "*", "ext-pdo": "*", "ext-zlib": "*", "aliyuncs/oss-sdk-php": "^2.4", "codingheping/code-diff": "^1.0", "codingheping/import": "^1.8", "codingheping/statement-component": "^1.0", "codingheping/web-task": "^1.1", "doctrine/sql-formatter": "^1.3", "kartik-v/yii2-widget-activeform": "v1.6.2", "kuainiu/yii2-kuainiu": "^2.1", "league/flysystem-sftp-v3": "^2.5", "mdmsoft/yii2-admin": "^2.0", "nesbot/carbon": "^2.52", "notamedia/yii2-sentry": "^1.7", "phpoffice/phpspreadsheet": "^1.18", "predis/predis": "^2.1", "symfony/expression-language": "^5.4", "symfony/polyfill": "^1.23", "waterank/audit": "~3.14", "xlerr/application-payment": "^1.1", "xlerr/cmdb": "^2.0", "xlerr/common-payment": "^1.0", "xlerr/cost-management": "^0.0", "xlerr/cpop-income": "^0.1", "xlerr/cpop-offline": "^0.0", "xlerr/datasource": "^4.1", "xlerr/desensitise": "^2.0", "xlerr/diy-report": "^0.0", "xlerr/fullcalendar": "^1.0", "xlerr/kvmanager": "^4.3", "xlerr/metric": "^0.0", "xlerr/sentry-television": "^1.0", "xlerr/settlement-flow": "^3.7", "xlerr/task": "^2.3", "xlerr/yii2-adminlte": "~3.1.0", "xlerr/yii2-common-widgets": "^1.18", "xlerr/yii2-widget-code-editor": "^1.0", "yiisoft/yii2": ">=2.0.51 <3.0", "yiisoft/yii2-bootstrap": "^2.0", "yiisoft/yii2-symfonymailer": "^3.0"}, "autoload": {"files": ["common/config/functions.php"]}, "require-dev": {"roave/security-advisories": "dev-latest", "symfony/var-dumper": "^5.2", "xlerr/yii2-gii-templates": "^2.0", "yiisoft/yii2-debug": "^2.1", "yiisoft/yii2-faker": "^2.0", "yiisoft/yii2-gii": "^2.2"}, "scripts": {"task": "./yii generate-dashboard-work && ./yii task/process-all", "start-backend": "php -S 127.0.0.1:9890 -t backend/web", "load-remote-config": "JAVA=/usr/bin/java vendor/bin/loadRemoteConfig"}, "config": {"secure-http": true, "sort-packages": true, "allow-plugins": {"composer/package-versions-deprecated": true, "yiisoft/yii2-composer": true, "php-http/discovery": true}}, "repositories": [{"type": "composer", "url": "https://biz-satis-test.kuainiujinke.com/composer"}, {"type": "composer", "url": "https://asset-packagist.org"}]}