<?xml version="1.0"?>
<psalm
        errorLevel="6"
        resolveFromConfigFile="true"
        useDocblockTypes="true"
        autoloader="psalm.bootstrap.php"
        cacheDirectory="console/runtime/psalm/cache"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="https://getpsalm.org/schema/config"
        xsi:schemaLocation="https://getpsalm.org/schema/config vendor/vimeo/psalm/config.xsd"
>
    <projectFiles>
        <directory name="backend"/>
        <directory name="common"/>
        <directory name="modules"/>
        <directory name="console"/>
        <ignoreFiles>
            <file name="common/config/bootstrap.php"/>
            <directory name="console/migrations"/>
            <directory name="console/runtime"/>
            <directory name="common/mail"/>
            <directory name="backend/views"/>
            <directory name="backend/runtime"/>
            <directory name="backend/web"/>
            <directory name="modules/dashboard/views"/>
            <directory name="modules/finance/views"/>
            <directory name="modules/grant/views"/>
            <directory name="modules/payment/views"/>
            <directory name="modules/repay/views"/>
            <directory name="modules/system/views"/>
            <directory name="modules/pcrawler/views"/>
        </ignoreFiles>
    </projectFiles>
</psalm>
