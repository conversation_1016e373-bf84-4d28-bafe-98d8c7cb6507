version: "3"

services:
  application:
    image: registry.cn-shanghai.aliyuncs.com/dx-biz/portal.base
    volumes:
      - ./:/data/www/wwwroot/portal:cached
      - ./docker/nginx/:/data/nginx/conf:cached
      - ./docker/php/php.ini:/etc/php.ini:cached
      - ./docker/php/php-fpm.conf:/etc/php-fpm.conf:cached

    ports:
      - 80:80
    networks:
      - brnet1
  redis:
    image: redis
    command: redis-server --appendonly yes
    networks:
      - brnet1

networks:
  brnet1: