<?php

/**
 * The manifest of files that are local to specific environment.
 * This file returns a list of environments that the application
 * may be installed under. The returned data must be in the following
 * format:
 *
 * ```php
 * return [
 *     'environment name' => [
 *         'path' => 'directory storing the local files',
 *         'skipFiles'  => [
 *             // list of files that should only copied once and skipped if they already exist
 *         ],
 *         'setWritable' => [
 *             // list of directories that should be set writable
 *         ],
 *         'setExecutable' => [
 *             // list of files that should be set executable
 *         ],
 *         'setCookieValidationKey' => [
 *             // list of config files that need to be inserted with automatically generated cookie validation keys
 *         ],
 *         'createSymlink' => [
 *             // list of symlinks to be created. Keys are symlinks, and values are the targets.
 *         ],
 *     ],
 * ];
 * ```
 */
return [
    'Development' => [
        'path' => 'dev',
        'setWritable' => [
            'backend/runtime',
            'backend/web/assets',
            'console/runtime',
        ],
        'setExecutable' => [
            'yii',
            'yii_test',
            'release',
        ],
        'setCookieValidationKey' => [
            'backend/config/main-local.php',
            'common/config/codeception-local.php',
        ],
    ],
    //泰国（测试）
    'test-tha-1' => [
        'path' => 'test-tha-1',
        'setWritable' => [
            'backend/runtime',
            'backend/web/assets',
            'console/runtime',
        ],
        'setExecutable' => [
            'yii',
            'yii_test',
            'release',
        ],
        'setCookieValidationKey' => [
            'backend/config/main-local.php',
            'common/config/codeception-local.php',
        ],
    ],
    //印尼（测试）
    'test-idn-1' => [
        'path' => 'test-idn-1',
        'setWritable' => [
            'backend/runtime',
            'backend/web/assets',
            'console/runtime',
        ],
        'setExecutable' => [
            'yii',
            'yii_test',
            'release',
        ],
        'setCookieValidationKey' => [
            'backend/config/main-local.php',
            'common/config/codeception-local.php',
        ],
    ],
    //巴基斯坦（测试）
    'test-pak-1' => [
        'path' => 'test-pak-1',
        'setWritable' => [
            'backend/runtime',
            'backend/web/assets',
            'console/runtime',
        ],
        'setExecutable' => [
            'yii',
            'yii_test',
            'release',
        ],
        'setCookieValidationKey' => [
            'backend/config/main-local.php',
            'common/config/codeception-local.php',
        ],
    ],
    //菲律宾（测试）
    'test-phl-1' => [
        'path' => 'test-phl-1',
        'setWritable' => [
            'backend/runtime',
            'backend/web/assets',
            'console/runtime',
        ],
        'setExecutable' => [
            'yii',
            'yii_test',
            'release',
        ],
        'setCookieValidationKey' => [
            'backend/config/main-local.php',
            'common/config/codeception-local.php',
        ],
    ],
    //墨西哥（测试）
    'test-mex-1' => [
        'path' => 'test-mex-1',
        'setWritable' => [
            'backend/runtime',
            'backend/web/assets',
            'console/runtime',
        ],
        'setExecutable' => [
            'yii',
            'yii_test',
            'release',
        ],
        'setCookieValidationKey' => [
            'backend/config/main-local.php',
            'common/config/codeception-local.php',
        ],
    ],
    //泰国（生产）
    'prod-tha' => [
        'path' => 'prod-tha',
        'setWritable' => [
            'backend/runtime',
            'backend/web/assets',
            'console/runtime',
        ],
        'setExecutable' => [
            'yii',
            'release',
        ],
        'setCookieValidationKey' => [
            'backend/config/main-local.php',
        ],
    ],
    //菲律宾（生产）
    'prod-phl' => [
        'path' => 'prod-phl',
        'setWritable' => [
            'backend/runtime',
            'backend/web/assets',
            'console/runtime',
        ],
        'setExecutable' => [
            'yii',
            'release',
        ],
        'setCookieValidationKey' => [
            'backend/config/main-local.php',
        ],
    ],
    //墨西哥（生产）
    'prod-mex' => [
        'path' => 'prod-mex',
        'setWritable' => [
            'backend/runtime',
            'backend/web/assets',
            'console/runtime',
        ],
        'setExecutable' => [
            'yii',
            'release',
        ],
        'setCookieValidationKey' => [
            'backend/config/main-local.php',
        ],
    ],
    //墨西哥（生产）
    'prod-mex-intl' => [
        'path' => 'prod-mex-intl',
        'setWritable' => [
            'backend/runtime',
            'backend/web/assets',
            'console/runtime',
        ],
        'setExecutable' => [
            'yii',
            'release',
        ],
        'setCookieValidationKey' => [
            'backend/config/main-local.php',
        ],
    ],
    //巴基斯坦
    'prod-pak' => [
        'path' => 'prod-pak',
        'setWritable' => [
            'backend/runtime',
            'backend/web/assets',
            'console/runtime',
        ],
        'setExecutable' => [
            'yii',
            'release',
        ],
        'setCookieValidationKey' => [
            'backend/config/main-local.php',
        ],
    ],
    //印尼
    'prod-idn' => [
        'path' => 'prod-idn',
        'setWritable' => [
            'backend/runtime',
            'backend/web/assets',
            'console/runtime',
        ],
        'setExecutable' => [
            'yii',
            'release',
        ],
        'setCookieValidationKey' => [
            'backend/config/main-local.php',
        ],
    ],

];
