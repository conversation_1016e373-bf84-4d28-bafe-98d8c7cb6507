<?php

$config = [
    'components' => [
        'request'              => [
            // !!! insert a secret key in the following (if it is empty) - this is required by cookie validation
            'cookieValidationKey' => '',
        ],
        'authClientCollection' => [
            'class'   => \yii\authclient\Collection::class,
            'clients' => [
                'kuainiu' => [
                    'class'        => \kuainiu\components\authclient\Kuainiu::class,
                    'clientId'     => '175',//根据oa应用配置填写具体值
                    'clientSecret' => 'AY7cvNWqk5kGuxCNqRLLNqdlM6n4FpT6TXtaGDL4',//根据oa应用配置填写具体值
                    'authUrl'      => 'https://passport.qianxi.info/oauth/authorize',
                    'tokenUrl'     => 'https://passport.qianxi.info/oauth/token',
                    'apiBaseUrl'   => 'https://passport.qianxi.info/api/v1/users/me',
                    'scope'        => 'user.basic',
                ],
            ],
        ],
    ],
];

if (!YII_ENV_PROD) {
    // configuration adjustments for 'dev' environment
    $config['bootstrap'][]      = 'debug';
    $config['modules']['debug'] = [
        'class' => 'yii\debug\Module',
    ];

    $config['bootstrap'][]    = 'gii';
    $config['modules']['gii'] = [
        'class' => 'yii\gii\Module',
    ];
}

return $config;
