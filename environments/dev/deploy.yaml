apiVersion: extensions/v1beta1
kind: Deployment
metadata:
  labels:
    name: global-portal-TEST_VERSION
  namespace: biz
  name: global-portal-TEST_VERSION
spec:
  replicas: 1
  selector:
    matchLabels:
      app: global-portal-TEST_VERSION
  template:
    metadata:
      labels:
        app: global-portal-TEST_VERSION
    spec:
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - global-portal-TEST_VERSION
                topologyKey: kubernetes.io/hostname
      imagePullSecrets:
        - name: aly-registry
      containers:
        - name: global-portal
          image: registry-vpc.cn-shanghai.aliyuncs.com/dx-biz/global-portal:TAG
          imagePullPolicy: Always
          env:
            - name: TZ
              value: Asia/Kolkata
            - name: JASYPT_PASSWD
              valueFrom:
                configMapKeyRef:
                  name: biz-cm
                  key: biz.jasypt.encryptor.password
            - name: BI<PERSON>_NACOS_CONFIG_SERVER_ADDR
              valueFrom:
                configMapKeyRef:
                  name: biz-cm
                  key: biz.nacos.config.server-addr
            - name: BIZ_NACOS_CONFIG_USERNAME
              valueFrom:
                configMapKeyRef:
                  name: biz-cm
                  key: biz.nacos.config.username
            - name: BIZ_NACOS_CONFIG_PASSWORD
              valueFrom:
                configMapKeyRef:
                  name: biz-cm
                  key: biz.nacos.config.password
          resources:
            requests:
              cpu: 100m
              memory: 128Mi
            limits:
              cpu: 4000m
              memory: 512Mi
