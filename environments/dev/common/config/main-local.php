<?php

use Predis\Client;
use Predis\Connection\Parameters;
use yii\symfonymailer\Mailer;

return [
    'components' => [
        'db'       => [
            'class'    => 'yii\db\Connection',
            'dsn'      => 'mysql:host=127.0.0.1;dbname=global_portal',
            'username' => 'root',
            'password' => '',
            'charset'  => 'utf8',
        ],
        'dbReadOnly'       => [
            'class'    => 'yii\db\Connection',
            'dsn'      => 'mysql:host=127.0.0.1;dbname=global_portal',
            'username' => 'root',
            'password' => '',
            'charset'  => 'utf8',
        ],
        'mqDb'     => [
            'class'    => 'yii\db\Connection',
            'dsn'      => 'mysql:host=127.0.0.1;dbname=global_mq',
            'username' => 'root',
            'password' => '',
            'charset'  => 'utf8',
        ],
        'mqDbReadOnly'     => [
            'class'    => 'yii\db\Connection',
            'dsn'      => 'mysql:host=127.0.0.1;dbname=global_mq',
            'username' => 'root',
            'password' => '',
            'charset'  => 'utf8',
        ],
        'dcsDb'    => [
            'class'    => 'yii\db\Connection',
            'dsn'      => 'mysql:host=127.0.0.1;dbname=global_dcs',
            'username' => 'root',
            'password' => '',
            'charset'  => 'utf8',
        ],
        'dcsDbReadOnly'    => [
            'class'    => 'yii\db\Connection',
            'dsn'      => 'mysql:host=127.0.0.1;dbname=global_dcs',
            'username' => 'root',
            'password' => '',
            'charset'  => 'utf8',
        ],
        'rbizDb'   => [
            'class'    => 'yii\db\Connection',
            'dsn'      => 'mysql:host=127.0.0.1;dbname=global_rbiz',
            'username' => 'root',
            'password' => '',
            'charset'  => 'utf8',
        ],
        'rbizDbReadOnly'   => [
            'class'    => 'yii\db\Connection',
            'dsn'      => 'mysql:host=127.0.0.1;dbname=global_rbiz',
            'username' => 'root',
            'password' => '',
            'charset'  => 'utf8',
        ],
        'gbizDb'   => [
            'class'    => 'yii\db\Connection',
            'dsn'      => 'mysql:host=127.0.0.1;dbname=global_gbiz',
            'username' => 'root',
            'password' => '',
            'charset'  => 'utf8',
        ],
        'gbizDbReadOnly'   => [
            'class'    => 'yii\db\Connection',
            'dsn'      => 'mysql:host=127.0.0.1;dbname=global_gbiz',
            'username' => 'root',
            'password' => '',
            'charset'  => 'utf8',
        ],
        'paySvrDb' => [
            'class'    => 'yii\db\Connection',
            'dsn'      => 'mysql:host=127.0.0.1;dbname=global_payment',
            'username' => 'root',
            'password' => '',
            'charset'  => 'utf8',
        ],
        'paySvrDbReadOnly' => [
            'class'    => 'yii\db\Connection',
            'dsn'      => 'mysql:host=127.0.0.1;dbname=global_payment',
            'username' => 'root',
            'password' => '',
            'charset'  => 'utf8',
        ],
        'dbCmdb'     => [
            'class'    => 'yii\db\Connection',
            'dsn'      => 'mysql:host=127.0.0.1;dbname=global_cmdb',
            'username' => 'root',
            'password' => '',
            'charset'  => 'utf8',
        ],
        'dbCmdbReadOnly'     => [
            'class'    => 'yii\db\Connection',
            'dsn'      => 'mysql:host=127.0.0.1;dbname=global_cmdb',
            'username' => 'root',
            'password' => '',
            'charset'  => 'utf8',
        ],
        'redis' => static function () {
            return new Client(
                new Parameters([
                    'scheme' => 'tcp',
                    'host' => '127.0.0.1',
                    'port' => 6379,
                    'database' => 8,
                ])
            );
        },
        'mailer'       => [
            'class'            => Mailer::class,
            'viewPath'         => '@common/mail',
            'useFileTransport' => true,//这句一定有，false发送邮件，true只是生成邮件在runtime文件夹下，不发邮件
            'transport'        => [
                'dsn' => 'smtps://<EMAIL>:<EMAIL>:465',
            ],
        ],
    ],
];
