<?php

use backend\assets\NotifyAsset;
use yii\web\JsExpression;
use yii\web\View;

return [
    'logo' => '泰国信贷平台',
    'adminlte' => [
        'customNavbar' => [
            'calendar' => [
                'order' => 3,
                'icon' => 'fa fa-fw fa-calendar-alt',
                'options' => [
                    'onclick' => new JsExpression(
                        '(() => window.makeDialog({type: 2, title: \'日历\', content: \'/event/event/fullcalendar\'}))()'
                    ),
                ],
            ],
            'bell' => [
                'order' => 4,
                'icon' => 'fa fa-fw fa-bell-o',
                'options' => [
                    'id' => 'openMsgMenu',
                ],
                'func' => static function (View $view): void {
                    NotifyAsset::register($view);
                },
            ],
        ],
    ],
];
