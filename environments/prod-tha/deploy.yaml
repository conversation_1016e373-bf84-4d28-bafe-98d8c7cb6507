apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    name: biz-prod
  namespace: biz
  name: biz-prod
spec:
  replicas: 1
  selector:
    matchLabels:
      app: biz-prod
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: biz-prod
    spec:
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - biz-prod
                topologyKey: kubernetes.io/hostname
      imagePullSecrets:
        - name: aly-hongkong-registry
      nodeSelector:
        node-biz: biz-only
      tolerations:
        - effect: NoExecute
          key: node-biz
          operator: Equal
          value: biz-only
      terminationGracePeriodSeconds: 60
      containers:
        - name: biz-prod
          image: registry.cn-hongkong.aliyuncs.com/dx-biz/global-portal:prod-tha-etr_20220721-dc30be4
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 15
            successThreshold: 1
            tcpSocket:
              port: 80
            timeoutSeconds: 5
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 15
            successThreshold: 1
            tcpSocket:
              port: 80
            timeoutSeconds: 5
          lifecycle:
            postStart:
              exec:
                command:
                  - /data/www/wwwroot/portal/init
                  - '--env=ENV'
                  - '--overwrite=y'
            preStop:
              exec:
                command:
                  - supervisorctl
                  - stop
                  - xxl-job
                  - '&&'
                  - sleep
                  - '10'
          env:
            - name: TZ
              valueFrom:
                configMapKeyRef:
                  name: biz-cm
                  key: user.timezone
            - name: PORTAL_DB_HOST
              value: rm-gs5f922lnv05x03ra.mysql.singapore.rds.aliyuncs.com
            - name: PORTAL_DB_NAME
              value: biz-portal
            - name: PORTAL_DB_USER
              valueFrom:
                configMapKeyRef:
                  name: biz-cm
                  key: biz.db.username
            - name: PORTAL_DB_PASSWD
              valueFrom:
                configMapKeyRef:
                  name: biz-cm
                  key: biz.db.password
            - name: JASYPT_PASSWD
              valueFrom:
                configMapKeyRef:
                  name: biz-cm
                  key: biz.jasypt.encryptor.password
            - name: BIZ_NACOS_CONFIG_SERVER_ADDR
              valueFrom:
                configMapKeyRef:
                  name: biz-cm
                  key: biz.nacos.config.server-addr
            - name: BIZ_NACOS_CONFIG_USERNAME
              valueFrom:
                configMapKeyRef:
                  name: biz-cm
                  key: biz.nacos.config.username
            - name: BIZ_NACOS_CONFIG_PASSWORD
              valueFrom:
                configMapKeyRef:
                  name: biz-cm
                  key: biz.nacos.config.password
          resources:
            requests:
              cpu: 100m
              memory: 256Mi
            limits:
              cpu: 1000m
              memory: 1280Mi

---
apiVersion: v1
kind: Service
metadata:
  name: biz-prod-svc
  namespace: biz
spec:
  clusterIP: None
  clusterIPs:
    - None
  ports:
    - name: http
      port: 80
      protocol: TCP
      targetPort: 80
  selector:
    app: biz-prod
  type: ClusterIP
status:
  loadBalancer: { }
