apiVersion: extensions/v1beta1
kind: Deployment
metadata:
  labels:
    name: biz-prod
  namespace: biz-pak
  name: biz-prod
spec:
  replicas: 1
  selector:
    matchLabels:
      app: biz-prod
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: biz-prod
    spec:
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - biz-prod
                topologyKey: kubernetes.io/hostname
      imagePullSecrets:
        - name: aly-hongkong-registry
      terminationGracePeriodSeconds: 60
      containers:
        - name: biz-prod
          image: registry.cn-hongkong.aliyuncs.com/dx-biz/global-portal:TAG
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 15
            successThreshold: 1
            tcpSocket:
              port: 80
            timeoutSeconds: 5
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 15
            successThreshold: 1
            tcpSocket:
              port: 80
            timeoutSeconds: 5
          lifecycle:
            postStart:
              exec:
                command:
                  - /data/www/wwwroot/portal/init
                  - '--env=ENV'
                  - '--overwrite=y'
            preStop:
              exec:
                command:
                  - supervisorctl
                  - stop
                  - xxl-job
                  - '&&'
                  - sleep
                  - '10'
          env:
            - name: TZ
              value: Asia/Manila
            - name: JASYPT_PASSWD
              valueFrom:
                configMapKeyRef:
                  name: biz-cm
                  key: biz.jasypt.encryptor.password
            - name: BIZ_NACOS_CONFIG_SERVER_ADDR
              valueFrom:
                configMapKeyRef:
                  name: biz-cm
                  key: biz.nacos.config.server-addr
            - name: BIZ_NACOS_CONFIG_USERNAME
              valueFrom:
                configMapKeyRef:
                  name: biz-cm
                  key: biz.nacos.config.username
            - name: BIZ_NACOS_CONFIG_PASSWORD
              valueFrom:
                configMapKeyRef:
                  name: biz-cm
                  key: biz.nacos.config.password
          resources:
            requests:
              cpu: 100m
              memory: 256Mi
            limits:
              cpu: 1000m
              memory: 1280Mi
