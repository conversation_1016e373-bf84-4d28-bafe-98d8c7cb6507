<?php

use kuainiu\components\authclient\Kuainiu;
use Predis\Client;
use yii\authclient\Collection;
use yii\db\Connection;
use yii\symfonymailer\Mailer;

return [
    'timeZone' => 'Asia/Bangkok',
    'components' => [
        'formatter' => [
            'defaultTimeZone' => 'Asia/Bangkok',
        ],
        'db' => [
            'class' => Connection::class,
            'dsn' => 'mysql:host=rm-uf6seazei9e71x831.mysql.rds.aliyuncs.com;dbname=tha_biz_portal',
            'username' => 'root',
            'password' => '0Q^0bBURuSvS3#PB',
            'charset' => 'utf8',
        ],
        'dbReadOnly' => [
            'class' => Connection::class,
            'dsn' => 'mysql:host=rm-uf6seazei9e71x831.mysql.rds.aliyuncs.com;dbname=tha_biz_portal',
            'username' => 'root',
            'password' => '0Q^0bBURuSvS3#PB',
            'charset' => 'utf8',
        ],
        'mqDb' => [
            'class' => Connection::class,
            'dsn' => 'mysql:host=rm-uf6seazei9e71x831.mysql.rds.aliyuncs.com;dbname=tha_biz_mq',
            'username' => 'root',
            'password' => '0Q^0bBURuSvS3#PB',
            'charset' => 'utf8',
        ],
        'mqDbReadOnly' => [
            'class' => Connection::class,
            'dsn' => 'mysql:host=rm-uf6seazei9e71x831.mysql.rds.aliyuncs.com;dbname=tha_biz_mq',
            'username' => 'root',
            'password' => '0Q^0bBURuSvS3#PB',
            'charset' => 'utf8',
        ],
        'dcsDb' => [
            'class' => Connection::class,
            'dsn' => 'mysql:host=rm-uf6seazei9e71x831.mysql.rds.aliyuncs.com;dbname=tha_dcs1',
            'username' => 'root',
            'password' => '0Q^0bBURuSvS3#PB',
            'charset' => 'utf8',
        ],
        'dcsDbReadOnly' => [
            'class' => Connection::class,
            'dsn' => 'mysql:host=rm-uf6seazei9e71x831.mysql.rds.aliyuncs.com;dbname=tha_dcs1',
            'username' => 'root',
            'password' => '0Q^0bBURuSvS3#PB',
            'charset' => 'utf8',
        ],
        'rbizDb' => [
            'class' => Connection::class,
            'dsn' => 'mysql:host=rm-uf6seazei9e71x831.mysql.rds.aliyuncs.com;dbname=tha_rbiz1',
            'username' => 'root',
            'password' => '0Q^0bBURuSvS3#PB',
            'charset' => 'utf8',
        ],
        'rbizDbReadOnly' => [
            'class' => Connection::class,
            'dsn' => 'mysql:host=rm-uf6seazei9e71x831.mysql.rds.aliyuncs.com;dbname=tha_rbiz1',
            'username' => 'root',
            'password' => '0Q^0bBURuSvS3#PB',
            'charset' => 'utf8',
        ],
        'gbizDb' => [
            'class' => Connection::class,
            'dsn' => 'mysql:host=rm-uf6seazei9e71x831.mysql.rds.aliyuncs.com;dbname=tha_gbiz1',
            'username' => 'root',
            'password' => '0Q^0bBURuSvS3#PB',
            'charset' => 'utf8',
        ],
        'gbizDbReadOnly' => [
            'class' => Connection::class,
            'dsn' => 'mysql:host=rm-uf6seazei9e71x831.mysql.rds.aliyuncs.com;dbname=tha_gbiz1',
            'username' => 'root',
            'password' => '0Q^0bBURuSvS3#PB',
            'charset' => 'utf8',
        ],
        'paySvrDb' => [
            'class' => Connection::class,
            'dsn' => 'mysql:host=rm-uf6seazei9e71x831.mysql.rds.aliyuncs.com;dbname=tha_payment_test1',
            'username' => 'root',
            'password' => '0Q^0bBURuSvS3#PB',
            'charset' => 'utf8',
        ],
        'paySvrDbReadOnly' => [
            'class' => Connection::class,
            'dsn' => 'mysql:host=rm-uf6seazei9e71x831.mysql.rds.aliyuncs.com;dbname=tha_payment_test1',
            'username' => 'root',
            'password' => '0Q^0bBURuSvS3#PB',
            'charset' => 'utf8',
        ],
        'dbCmdb' => [
            'class' => Connection::class,
            'dsn' => 'mysql:host=rm-uf6seazei9e71x831.mysql.rds.aliyuncs.com;dbname=tha_cmdb1',
            'username' => 'root',
            'password' => '0Q^0bBURuSvS3#PB',
            'charset' => 'utf8',
        ],
        'dbCmdbReadOnly' => [
            'class' => Connection::class,
            'dsn' => 'mysql:host=rm-uf6seazei9e71x831.mysql.rds.aliyuncs.com;dbname=tha_cmdb1',
            'username' => 'root',
            'password' => '0Q^0bBURuSvS3#PB',
            'charset' => 'utf8',
        ],
        'dbStatement' => [
            'class' => Connection::class,
            'dsn' => 'mysql:host=rm-uf6seazei9e71x831.mysql.rds.aliyuncs.com;dbname=global_statement',
            'username' => 'root',
            'password' => '0Q^0bBURuSvS3#PB',
            'charset' => 'utf8',
        ],
        'redis' => [
            'class' => Client::class,
            '__construct()' => [
                'parameters' => [
                    'scheme' => 'tcp',
                    'host' => 'biz-redis-svc.biz-tha.svc.cluster.local',
                    'port' => 6379,
                    'database' => 1,
                    'password' => 'biz&redis',
                ],
            ],
        ],
        'mailer' => [
            'class' => Mailer::class,
            'viewPath' => '@common/mail',
            'useFileTransport' => true,//这句一定有，false发送邮件，true只是生成邮件在runtime文件夹下，不发邮件
            'transport' => [
                'dsn' => 'smtps://<EMAIL>:<EMAIL>:465',
            ],
        ],
        'authClientCollection' => [
            'class' => Collection::class,
            'clients' => [
                'kuainiu' => [
                    'class' => Kuainiu::class,
                    'clientId' => 508,//根据oa应用配置填写具体值
                    'clientSecret' => 'qRXuTgk2BdJzQSDMu9bR2nYOZOQVu9yqb44jABin',//根据oa应用配置填写具体值
                    'authUrl' => 'https://stage-oa.kuainiu.io/account/oauth/authorize',
                    'tokenUrl' => 'https://stage-oa.kuainiu.io/account/oauth/token',
                    'apiBaseUrl' => 'https://stage-oa.kuainiu.io/account/api/v2/users/me',
                    'scope' => 'user.basic',
                ],
            ],
        ],
    ],
];
