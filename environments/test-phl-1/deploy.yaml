apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    name: global-portal-TEST_VERSION
  namespace: biz-phl
  name: global-portal-TEST_VERSION
spec:
  replicas: 1
  selector:
    matchLabels:
      app: global-portal-TEST_VERSION
  template:
    metadata:
      labels:
        app: global-portal-TEST_VERSION
    spec:
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - global-portal-TEST_VERSION
                topologyKey: kubernetes.io/hostname
      imagePullSecrets:
        - name: aly-ap-southeast-1-registry
      terminationGracePeriodSeconds: 60
      containers:
        - name: global-portal
          image: overseas-registry.ap-southeast-1.cr.aliyuncs.com/biz/global-portal:TAG
          imagePullPolicy: Always
          lifecycle:
            postStart:
              exec:
                command:
                  - /data/www/wwwroot/portal/init
                  - '--env=ENV'
                  - '--overwrite=y'
            preStop:
              exec:
                command:
                  - supervisorctl
                  - stop
                  - xxl-job
                  - '&&'
                  - sleep
                  - '10'
          env:
            - name: TZ
              value: Asia/Manila
            - name: PORTAL_DB_HOST
              value: rm-uf6589ct883rjc052.mysql.rds.aliyuncs.com
            - name: PORTAL_DB_NAME
              value: phl_biz_portal
            - name: PORTAL_DB_USER
              valueFrom:
                configMapKeyRef:
                  name: biz-cm
                  key: biz.db.username
            - name: PORTAL_DB_PASSWD
              valueFrom:
                configMapKeyRef:
                  name: biz-cm
                  key: biz.db.password
            - name: JASYPT_PASSWD
              valueFrom:
                configMapKeyRef:
                  name: biz-cm
                  key: biz.jasypt.encryptor.password
            - name: BIZ_NACOS_CONFIG_SERVER_ADDR
              valueFrom:
                configMapKeyRef:
                  name: biz-cm
                  key: biz.nacos.config.server-addr
            - name: BIZ_NACOS_CONFIG_USERNAME
              valueFrom:
                configMapKeyRef:
                  name: biz-cm
                  key: biz.nacos.config.username
            - name: BIZ_NACOS_CONFIG_PASSWORD
              valueFrom:
                configMapKeyRef:
                  name: biz-cm
                  key: biz.nacos.config.password
          resources:
            requests:
              cpu: 100m
              memory: 128Mi
            limits:
              cpu: 1000m
              memory: 1280Mi
