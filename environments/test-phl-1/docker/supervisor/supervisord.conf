;
; For more information on the config file, please see:
; http://supervisord.org/configuration.html
;
; Notes:
;  - Shell expansion ("~" or "$HOME") is not supported.  Environment
;    variables can be expanded using this syntax: "%(ENV_HOME)s".
;  - Quotes around values are not supported, except in the case of
;    the environment= options as shown below.
;  - Comments must have a leading space: "a=b ;comment" not "a=b;comment".
;  - Command will be truncated if it looks like a config file comment, e.g.
;    "command=bash -c 'foo ; bar'" will truncate to "command=bash -c 'foo ".

[unix_http_server]
file=/var/run/supervisor.sock   ; the path to the socket file
chmod=0770                 ; socket file mode (default 0700)
chown=root:nginx		   ; socket file uid:gid owner
; username=user              ; default is no username (open server)
; password=123               ; default is no password (open server)

; [inet_http_server]         ; inet (TCP) server disabled by default
; port=127.0.0.1:9000        ; ip_address:port specifier, *:port for all iface

[supervisord]
logfile=/tmp/supervisord.log ; main log file; default $CWD/supervisord.log
logfile_maxbytes=100MB        ; max main logfile bytes b4 rotation; default 50MB
logfile_backups=10           ; # of main logfile backups; 0 means none, default 10
loglevel=info                ; log level; default info; others: debug,warn,trace
pidfile=/tmp/supervisord.pid ; supervisord pidfile; default supervisord.pid
nodaemon=false               ; start in foreground if true; default false
minfds=65535		     ; min. avail startup file descriptors; default 1024
minprocs=65536               ; min. avail process descriptors;default 200

; The rpcinterface:supervisor section must remain in the config file for
; RPC (supervisorctl/web interface) to work.  Additional interfaces may be
; added by defining them in separate [rpcinterface:x] sections.

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///var/run/supervisor.sock ; use a unix:// URL  for a unix socket
;serverurl=http://127.0.0.1:9000 ; use an http:// url to specify an inet socket
history_file=~/.sc_history  ; use readline history if available

[program:nginx]
command=/usr/local/bin/nginx -g "daemon off;"
user=root
stdout_logfile=/var/log/nginx.log
autostart=true
autorestart=true
sartsecs=0
startretries=1
redirect_stderr=true

[program:php-fpm]
command=/usr/sbin/php-fpm -F
user=nginx
stdout_logfile=/var/log/php-fpm.log
autostart=true
autorestart=true
sartsecs=5
startretries=5
redirect_stderr=true

[program:xxl-job]
command=java -Duser.timezone=Asia/Manila -Dfile.encoding=UTF-8 -Xms256M -Xmx256M -XX:+UnlockDiagnosticVMOptions -XX:+UnlockExperimentalVMOptions -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/data/logs -Xloggc:/data/logs/gc.log -jar xxl-job-executor-glue.jar --xxl.job.executor.appname=portal-phl-test --xxl.job.admin.addresses=http://xxl-job-admin-svc.biz-tha.svc.cluster.local:8080/xxl-job-admin
user=nginx
stdout_logfile=/var/log/xxl-job.log
autostart=true
autorestart=true
sartsecs=5
startretries=5
redirect_stderr=true

[program:init]
command=composer run load-remote-config
autorestart=false
sartsecs=7
startretries=5
directory=/data/www/wwwroot/portal

[include]
files = /etc/supervisord.d/*.conf
